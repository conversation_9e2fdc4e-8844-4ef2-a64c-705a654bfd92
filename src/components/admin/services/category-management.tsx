'use client'

// Imports
import React, { useState, useEffect, useCallback, useMemo } from 'react'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  FolderIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  EyeIcon,
  EyeSlashIcon,
  MagnifyingGlassIcon,
  ListBulletIcon,
  Squares2X2Icon,
  XMarkIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  FunnelIcon,
  AdjustmentsHorizontalIcon,
  ViewColumnsIcon
} from '@heroicons/react/24/outline'
import { CategoryHeader } from './category-header'
import { ConfirmationModal } from '../shared/confirmation-modal'
import { CategoryFormModal } from './category-form-modal'
import { ResponsivePagination } from '../shared/responsive-pagination'

// Type Definitions
interface Category {
  id: string
  name: string
  description?: string
  parentId?: string // Optional parent for hierarchical categories
  isActive: boolean
  displayOrder: number // Controls sorting order in lists
  children?: Category[] // Nested subcategories
  _count?: {
    services: number // Number of services in this category
    children: number // Number of subcategories
  }
}

interface CategoryManagementProps {
  selectedCategory: Category | null
  onCategorySelect: (category: Category | null) => void
  showSuccess: (title: string, message: string) => void
  showError: (title: string, message: string) => void
  showWarning: (title: string, message: string) => void
  showInfo: (title: string, message: string) => void
  showLoading: (title: string, message: string) => string
  clearLoadingNotifications: () => void
}

interface CategoryFormData {
  name: string
  description: string
  parentId: string // Empty string means root category
  isActive: boolean
  displayOrder: number
}

type ViewMode = 'list' | 'grid' // Display modes for categories
type Density = 'compact' | 'comfortable' | 'spacious' // UI density settings
type SortDirection = 'asc' | 'desc'

interface DeleteConfirmation {
  isOpen: boolean
  category: Category | null
  isBulkDelete?: boolean // Flag to indicate if this is a bulk delete operation
  bulkCategories?: Category[] // Categories to be deleted in bulk
  verificationData?: {
    canDelete: boolean // Whether category can be safely deleted
    reason?: string // Explanation for deletion status
    subcategories?: number // Count of child categories
    services?: number // Count of associated services
    dependencies?: string[] // List of dependency descriptions
  }
  showVerification?: boolean // Whether to show verification details
  title?: string // Modal title
  message?: string // Modal message
  details?: string // Modal details
}

// Constants and Utilities
// Dynamic icon selection based on category name keywords
const getCategoryIcon = (category: Category): string => {
  const name = category.name.toLowerCase()
  
  // Icon mapping with keywords and corresponding FontAwesome icons
  const iconMap = [
    { keywords: ['web', 'website', 'frontend', 'backend'], icon: 'fa-globe text-blue-500' },
    { keywords: ['mobile', 'app', 'ios', 'android'], icon: 'fa-mobile-alt text-green-500' },
    { keywords: ['design', 'ui', 'ux', 'graphic'], icon: 'fa-palette text-purple-500' },
    { keywords: ['ecommerce', 'shop', 'store', 'commerce'], icon: 'fa-shopping-cart text-orange-500' },
    { keywords: ['marketing', 'seo', 'social', 'advertising'], icon: 'fa-bullhorn text-red-500' },
    { keywords: ['consulting', 'strategy', 'business'], icon: 'fa-handshake text-indigo-500' },
    { keywords: ['support', 'maintenance', 'hosting'], icon: 'fa-tools text-gray-500' },
    { keywords: ['security', 'ssl', 'backup'], icon: 'fa-shield-alt text-yellow-500' },
    { keywords: ['analytics', 'tracking', 'report'], icon: 'fa-chart-line text-teal-500' }
  ]
  
  // Find matching icon based on category name keywords
  for (const { keywords, icon } of iconMap) {
    if (keywords.some(keyword => name.includes(keyword))) {
      return icon
    }
  }
  
  // Default icons based on category hierarchy
  return category.parentId ? 'fa-tag text-orange-500' : 'fa-layer-group text-blue-500'
}

// Main Component
export const CategoryManagement = React.memo<CategoryManagementProps>(({ 
  selectedCategory, 
  onCategorySelect, 
  showSuccess, 
  showError, 
  showWarning, 
  showInfo, 
  showLoading, 
  clearLoadingNotifications 
}) => {
  // State Management
  const [categories, setCategories] = useState<Category[]>([])
  const [categoriesKey, setCategoriesKey] = useState(0) // Force re-render key
  
  const [filteredCategories, setFilteredCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]) // Multi-select state
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set()) // Tree expansion state
  const [searchQuery, setSearchQuery] = useState('')
  const [currentFilters, setCurrentFilters] = useState<Record<string, string>>({})
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [gridColumns, setGridColumns] = useState<number>(3)
  const [density, setDensity] = useState<Density>('compact')
  
  // Default column visibility based on density setting
  const getDefaultColumns = useCallback((density: Density) => ({
    name: true,
    description: true,
    services: true,
    status: true,
    actions: true
  }), [])

  const [visibleColumns, setVisibleColumns] = useState(getDefaultColumns('compact'))
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnSelector, setShowColumnSelector] = useState(false)
  const [showWindowList, setShowWindowList] = useState(false)
  const [showBulkActions, setShowBulkActions] = useState(false)
  
  const [sortField, setSortField] = useState<string>('')
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')
  
  const [deleteConfirmation, setDeleteConfirmation] = useState<DeleteConfirmation>({ 
    isOpen: false, 
    category: null 
  })
  
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  
  const [formData, setFormData] = useState<CategoryFormData>({
    name: '',
    description: '',
    parentId: '',
    isActive: true,
    displayOrder: 0
  })

  // Event Handlers
  // Toggle category selection for bulk operations
  const handleSelectCategory = useCallback((categoryId: string) => {
    setSelectedCategories(prev => {
      const newSelection = prev.includes(categoryId) 
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
      
      setShowBulkActions(newSelection.length > 0)
      return newSelection
    })
  }, [])

  // Select/deselect all categories in current view
  const handleSelectAll = useCallback(() => {
    if (selectedCategories.length === filteredCategories.length) {
      setSelectedCategories([])
      setShowBulkActions(false)
    } else {
      setSelectedCategories(filteredCategories.map(category => category.id))
      setShowBulkActions(true)
    }
  }, [selectedCategories.length, filteredCategories])

  // Reset form to default values
  const resetForm = useCallback(() => {
    setFormData({
      name: '',
      description: '',
      parentId: '',
      isActive: true,
      displayOrder: 0
    })
    setEditingCategory(null)
  }, [])

  // Filter configuration for dynamic filter generation
  const filters = useMemo(() => [
    {
      key: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { value: '', label: 'All Status' },
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' }
      ]
    },
    {
      key: 'parent',
      label: 'Parent Category',
      type: 'select' as const,
      options: [
        { value: '', label: 'All Categories' },
        { value: 'root', label: 'Root Categories' },
        { value: 'sub', label: 'Sub Categories' }
      ]
    }
  ], [])

  // Data Fetching
  useEffect(() => {
    fetchCategories()
  }, [])

  // Re-filter and sort when dependencies change
  useEffect(() => {
    filterAndSortCategories()
  }, [categories, searchQuery, currentFilters, sortField, sortDirection])

  // Utility Functions
  // Handle click outside dropdowns to close them
  const handleClickOutside = useCallback((event: MouseEvent) => {
    const target = event.target as Node
    
    if (!(target instanceof Element) || !target.closest('.dropdown-container')) {
      setShowColumnSelector(false)
      setShowWindowList(false)
      setShowFilters(false)
    }
  }, [])

  // Global click listener for closing dropdowns
  useEffect(() => {
    if (showColumnSelector || showWindowList || showFilters) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showColumnSelector, showWindowList, showFilters, handleClickOutside])



  // Complex filtering and sorting logic
  const filterAndSortCategories = useCallback(() => {
    let filtered = [...categories]

    // Apply search filter
    if (searchQuery.trim()) {
      const searchLower = searchQuery.toLowerCase()
      filtered = filtered.filter(category =>
        category.name.toLowerCase().includes(searchLower) ||
        (category.description && category.description.toLowerCase().includes(searchLower))
      )
    }

    // Apply status filter
    if (currentFilters.status) {
      if (currentFilters.status === 'active') {
        filtered = filtered.filter(category => category.isActive)
      } else if (currentFilters.status === 'inactive') {
        filtered = filtered.filter(category => !category.isActive)
      }
    }

    // Apply parent category filter
    if (currentFilters.parent) {
      if (currentFilters.parent === 'root') {
        filtered = filtered.filter(category => !category.parentId)
      } else if (currentFilters.parent === 'sub') {
        filtered = filtered.filter(category => category.parentId)
      }
    }

    // Apply sorting
    if (sortField) {
      filtered.sort((a, b) => {
        let aValue: any = a[sortField as keyof Category]
        let bValue: any = b[sortField as keyof Category]

        // Normalize values for comparison
        if (sortField === 'name' || sortField === 'description') {
          aValue = (aValue || '').toLowerCase()
          bValue = (bValue || '').toLowerCase()
        } else if (sortField === 'isActive') {
          aValue = aValue ? 1 : 0
          bValue = bValue ? 1 : 0
        } else if (sortField === 'displayOrder') {
          aValue = aValue || 0
          bValue = bValue || 0
        } else if (sortField === 'services') {
          aValue = a._count?.services || 0
          bValue = b._count?.services || 0
        }

        // Apply sort direction
        if (sortDirection === 'asc') {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
        } else {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
        }
      })
    }

    setFilteredCategories(filtered)
    setCurrentPage(1) // Reset to first page when filtering
  }, [categories, searchQuery, currentFilters, sortField, sortDirection])
  
  // Pagination Logic
  const paginationData = useMemo(() => {
    const totalPages = Math.ceil(filteredCategories.length / itemsPerPage)
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    const paginatedCategories = filteredCategories.slice(startIndex, endIndex)
    
    return { totalPages, startIndex, endIndex, paginatedCategories }
  }, [filteredCategories, itemsPerPage, currentPage])
  
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page)
  }, [])
  
  const handleItemsPerPageChange = useCallback((items: number) => {
    setItemsPerPage(items)
    setCurrentPage(1) // Reset to first page when changing items per page
  }, [])
  
  // Calculate space needed for dropdowns to prevent layout shift
  const dropdownSpaceNeeded = useMemo(() => {
    let spaceNeeded = 0
    
    if (showColumnSelector) spaceNeeded = Math.max(spaceNeeded, 200)
    if (showWindowList) spaceNeeded = Math.max(spaceNeeded, 120)
    if (showFilters) spaceNeeded = Math.max(spaceNeeded, 300)
    
    return spaceNeeded
  }, [showColumnSelector, showWindowList, showFilters])

  // Data Processing
  // Build hierarchical category tree from flat API data
  const buildCategoryTree = useCallback((flatCategories: any[]): Category[] => {
    const categoryMap = new Map()
    const rootCategories: Category[] = []

    // Convert flat data to Category objects with proper field mapping
    flatCategories.forEach(cat => {
      const category: Category = {
        id: String(cat.id),
        name: cat.categname || cat.name, // Handle different API field names
        description: cat.categdesc || cat.description,
        parentId: cat.parentid ? String(cat.parentid) : undefined,
        isActive: cat.isActive !== undefined ? Boolean(cat.isActive) : (cat.isactive !== undefined ? Boolean(cat.isactive) : true),
        displayOrder: cat.displayorder || 0,
        children: [],
        _count: cat._count
      }
      
      categoryMap.set(category.id, category)
    })

    // Build parent-child relationships
    categoryMap.forEach(category => {
      if (category.parentId && categoryMap.has(category.parentId)) {
        categoryMap.get(category.parentId).children.push(category)
      } else {
        rootCategories.push(category)
      }
    })

    // Recursively sort categories by display order
    const sortCategories = (cats: Category[]) => {
      cats.sort((a, b) => a.displayOrder - b.displayOrder)
      cats.forEach(cat => {
        if (cat.children) {
          sortCategories(cat.children)
        }
      })
    }

    sortCategories(rootCategories)
    
    return rootCategories
  }, [])

  // Fetch categories from API and build hierarchical structure
  const fetchCategories = useCallback(async (showSuccessNotification = true) => {
    try {
      setLoading(true)
      if (showSuccessNotification) {
        showInfo('Loading Categories', 'Retrieving category data from the database...')
      }
      
      const response = await fetch('/api/admin/categories?limit=100')

      if (response.ok) {
        const data = await response.json()
        const categoriesData = data.data || data.categories || []
        
        // Build hierarchical tree structure from flat data
        const newCategories = buildCategoryTree(categoriesData)
        
        setCategories(newCategories)
        setCategoriesKey(prev => prev + 1) // Force re-render for table updates
        if (showSuccessNotification) {
          showSuccess('Categories Loaded', `Successfully loaded ${categoriesData.length} categories.`)
        }
      } else {
        console.error('Failed to fetch categories:', response.status, response.statusText)
        setCategories([])
        if (showSuccessNotification) {
          showError('Failed to Load Categories', 'Unable to retrieve categories from the server.')
        }
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
      setCategories([])
      if (showSuccessNotification) {
        showError('Network Error', 'Failed to connect to the server while loading categories.')
      }
    } finally {
      setLoading(false)
    }
  }, [buildCategoryTree, showInfo, showSuccess, showError])

  // CRUD Operations
  // Initialize edit form with category data
  const handleEdit = useCallback((category: Category) => {
    setEditingCategory(category)
    setFormData({
      name: category.name || '',
      description: category.description || '',
      parentId: category.parentId || '',
      isActive: category.isActive ?? true,
      displayOrder: category.displayOrder ?? 0
    })
    setIsFormOpen(true)
    showInfo('Edit Category', `Editing category: "${category.name}"`)
  }, [showInfo])

  // Verify category can be deleted before showing confirmation
  const handleDelete = useCallback(async (category: Category) => {
    try {
      showLoading('Verifying Category', `Checking if "${category.name}" can be deleted...`)
      
      const response = await fetch(`/api/admin/categories/${category.id}`)
      
      if (response.ok) {
        const categoryData = await response.json()
        const freshCategory = categoryData.data
        const verificationData = {
          canDelete: true,
          reason: 'Category is safe to delete',
          subcategories: 0,
          services: 0,
          dependencies: [] as string[]
        }

        // Check for subcategories dependency
        const subcategoriesCount = freshCategory._count?.children || 0
        if (subcategoriesCount > 0) {
          verificationData.canDelete = false
          verificationData.reason = 'Category has subcategories that must be removed first'
          verificationData.subcategories = subcategoriesCount
          verificationData.dependencies.push(`${subcategoriesCount} subcategor${subcategoriesCount === 1 ? 'y' : 'ies'}`)
        }

        // Check for services dependency
        const servicesCount = freshCategory._count?.services || 0
        if (servicesCount > 0) {
          verificationData.canDelete = false
          verificationData.reason = 'Category has associated services that must be removed first'
          verificationData.services = servicesCount
          verificationData.dependencies.push(`${servicesCount} service${servicesCount === 1 ? '' : 's'}`)
        }

        setDeleteConfirmation({ 
          isOpen: true, 
          category,
          verificationData,
          showVerification: true
        })
      } else {
        showError('Verification Failed', 'Unable to verify category deletion requirements.')
      }
    } catch (error) {
      console.error('Error verifying category:', error)
      showError('Verification Error', 'Failed to verify category deletion requirements.')
    }
  }, [showLoading, showError])

  // Execute bulk delete after confirmation
  const executeBulkDelete = useCallback(async () => {
    if (!deleteConfirmation.bulkCategories) return

    try {
      // If not all categories can be deleted, only delete the safe ones
      if (!deleteConfirmation.verificationData?.canDelete) {
        showLoading('Deleting Safe Categories', 'Deleting only categories without dependencies...')
        
        // Re-verify each category to get fresh data
        const verificationPromises = deleteConfirmation.bulkCategories.map(async (category) => {
          try {
            const response = await fetch(`/api/admin/categories/${category.id}`)
            if (response.ok) {
              const categoryData = await response.json()
              const freshCategory = categoryData.data
              const subcategoriesCount = freshCategory._count?.children || 0
              const servicesCount = freshCategory._count?.services || 0
              return {
                category,
                canDelete: subcategoriesCount === 0 && servicesCount === 0
              }
            }
            return { category, canDelete: false }
          } catch (error) {
            return { category, canDelete: false }
          }
        })
        
        const verificationResults = await Promise.all(verificationPromises)
        const safeToDelete = verificationResults.filter(result => result.canDelete)
        const unsafeToDelete = verificationResults.filter(result => !result.canDelete)
        
        if (safeToDelete.length === 0) {
          showWarning('No Safe Categories', 'None of the selected categories can be deleted due to dependencies.')
          setDeleteConfirmation({ isOpen: false, category: null })
          return
        }
        
        // Delete only safe categories
        const deletePromises = safeToDelete.map(result => 
          fetch(`/api/admin/categories/${result.category.id}`, {
            method: 'DELETE',
          })
        )
        
        await Promise.all(deletePromises)
        
        let message = `Successfully deleted ${safeToDelete.length} categories.`
        if (unsafeToDelete.length > 0) {
          message += ` ${unsafeToDelete.length} categories were skipped due to dependencies.`
        }
        
        showSuccess('Categories Deleted', message)
      } else {
        // All categories are safe to delete
        showLoading('Deleting Categories', `Deleting ${deleteConfirmation.bulkCategories.length} categories...`)
        
        const promises = deleteConfirmation.bulkCategories.map(category => 
          fetch(`/api/admin/categories/${category.id}`, {
            method: 'DELETE',
          })
        )

        await Promise.all(promises)
        showSuccess('Categories Deleted', `Successfully deleted ${deleteConfirmation.bulkCategories.length} categories.`)
      }
      
      await fetchCategories()
      setSelectedCategories([])
      setShowBulkActions(false)
    } catch (error) {
      console.error('Error deleting categories:', error)
      showError('Delete Error', 'Failed to delete selected categories.')
    } finally {
      setDeleteConfirmation({ isOpen: false, category: null })
    }
  }, [deleteConfirmation.bulkCategories, deleteConfirmation.verificationData, showLoading, showSuccess, showWarning, fetchCategories, showError])

  // Execute category deletion after verification
  const confirmDelete = useCallback(async () => {
    if (deleteConfirmation.isBulkDelete) {
      await executeBulkDelete()
      return
    }

    if (!deleteConfirmation.category) return

    const category = deleteConfirmation.category
    try {
      showLoading('Deleting Category', `Removing "${category.name}" from the system...`)
      
      const response = await fetch(`/api/admin/categories/${category.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        const result = await response.json()
        showSuccess('Category Deleted', result.message || `"${category.name}" has been successfully removed.`)
        await fetchCategories()
        // Clear selection if deleted category was selected
        if (selectedCategory?.id === category.id) {
          onCategorySelect(null)
        }
      } else {
        const errorData = await response.json()
        showError('Delete Failed', errorData.error || 'Failed to delete the category.')
      }
    } catch (error) {
      console.error('Error deleting category:', error)
      showError('Delete Error', 'An unexpected error occurred while deleting the category.')
    } finally {
      setDeleteConfirmation({ isOpen: false, category: null })
    }
  }, [deleteConfirmation.isBulkDelete, deleteConfirmation.category, executeBulkDelete, showLoading, showSuccess, fetchCategories, selectedCategory?.id, onCategorySelect, showError])

  const cancelDelete = useCallback(() => {
    clearLoadingNotifications()
    setDeleteConfirmation({ 
      isOpen: false, 
      category: null, 
      isBulkDelete: false,
      bulkCategories: undefined,
      verificationData: undefined, 
      showVerification: false 
    })
  }, [clearLoadingNotifications])

  // Toggle category active/inactive status
  const handleToggleActive = useCallback(async (category: Category) => {
    try {
      const newStatus = !category.isActive
      const action = newStatus ? 'activate' : 'deactivate'
      
      showLoading(`${action.charAt(0).toUpperCase() + action.slice(1)}ing Category`, 
        `${action.charAt(0).toUpperCase() + action.slice(1)}ing "${category.name}"...`)
      
      // Prepare request body with API field names
      const requestBody = {
        categname: category.name,
        categdesc: category.description,
        parentid: category.parentId ? Number(category.parentId) : 0,
        isactive: newStatus,
        displayorder: category.displayOrder
      }
      
      const response = await fetch(`/api/admin/categories/${category.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      })
      
      if (response.ok) {
        const result = await response.json()
        showSuccess(
          `Category ${action.charAt(0).toUpperCase() + action.slice(1)}d`, 
          result.message || `"${category.name}" has been ${action}d successfully.`
        )
        await fetchCategories(false) // Refresh without success notification
      } else {
        const errorData = await response.json()
        console.error('Error updating category status:', errorData)
        showError('Status Update Failed', errorData.error || 'Failed to update category status.')
      }
    } catch (error) {
      console.error('Error toggling category status:', error)
      showError('Update Error', 'An unexpected error occurred while updating the category status.')
    }
  }, [showLoading, showSuccess, fetchCategories, showError])

  // Toggle category tree expansion state
  const toggleExpanded = useCallback((categoryId: string) => {
    setExpandedCategories(prev => {
      const newExpanded = new Set(prev)
      if (newExpanded.has(categoryId)) {
        newExpanded.delete(categoryId)
      } else {
        newExpanded.add(categoryId)
      }
      return newExpanded
    })
  }, [])

  // Rendering Components
  // Recursive category row component for hierarchical display
  const CategoryRow = ({ 
    category, 
    level = 0, 
    isSelected, 
    onCategorySelect, 
    onEdit, 
    onToggleActive, 
    onDelete, 
    expandedCategories, 
    onToggleExpanded,
    isChecked,
    onCheck
  }: {
    category: Category
    level?: number
    isSelected: boolean
    onCategorySelect: (category: Category) => void
    onEdit: (category: Category) => void
    onToggleActive: (category: Category) => void
    onDelete: (category: Category) => void
    expandedCategories: Set<string>
    onToggleExpanded: (categoryId: string) => void
    isChecked: boolean
    onCheck: (categoryId: string) => void
  }) => {
    const isExpanded = expandedCategories.has(category.id)
    const hasChildren = category.children && category.children.length > 0
    const isCurrentlySelected = selectedCategory && selectedCategory.id === category.id

    return (
      <>
        <tr
          className={`hover:bg-gray-50 cursor-pointer ${
            isCurrentlySelected ? 'bg-blue-50' : ''
          } ${
            isChecked ? 'bg-blue-50 border-l-4 border-blue-500' : ''
          } ${density === 'compact' ? 'py-0' : density === 'spacious' ? 'py-4' : 'py-2'}`}
          onClick={() => onCategorySelect(category)}
        >
          <td className={`pl-2 whitespace-nowrap ${density === 'compact' ? 'py-0' : density === 'spacious' ? 'py-2' : 'py-1'}`} style={{ width: '6px' }}>
            <input
              type="checkbox"
              checked={isChecked}
              onChange={() => onCheck(category.id)}
              onClick={(e) => e.stopPropagation()}
              className={`text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${
                density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'
              }`}
            />
          </td>
          {visibleColumns.name && (
            <td className={`pl-1 pr-6 whitespace-nowrap w-24 ${density === 'compact' ? 'py-0' : density === 'spacious' ? 'py-2' : 'py-1'}`}>
            <div className="flex items-center">
              {/* Expand/Collapse Button */}
                <div className={density === 'compact' ? 'w-4' : density === 'spacious' ? 'w-5' : 'w-4'}>
                {hasChildren ? (
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      onToggleExpanded(category.id)
                    }}
                    className="p-0.5"
                  >
                    {isExpanded ? (
                        <ChevronDownIcon className={`text-gray-500 ${density === 'compact' ? 'h-3 w-3' : density === 'spacious' ? 'h-4 w-4' : 'h-3 w-3'}`} />
                    ) : (
                        <ChevronRightIcon className={`text-gray-500 ${density === 'compact' ? 'h-3 w-3' : density === 'spacious' ? 'h-4 w-4' : 'h-3 w-3'}`} />
                    )}
                  </button>
                ) : null}
              </div>

              {/* Category Icon */}
                <div className={`flex items-center justify-center mr-1 ${density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-10 h-10' : 'w-8 h-8'}`}>
                  <i className={`fas ${getCategoryIcon(category)} ${density === 'compact' ? 'text-lg' : density === 'spacious' ? 'text-xl' : 'text-lg'}`}></i>
              </div>

              {/* Category Name */}
              <div className="flex-1 min-w-0">
                  <span className={`text-gray-500 truncate font-bold ${density === 'compact' ? 'text-sm' : density === 'spacious' ? 'text-base' : 'text-sm'}`}>
                  {category.name}
                </span>
              </div>
            </div>
          </td>
          )}

          {visibleColumns.description && (
            <td className={`px-6 whitespace-nowrap w-80 ${density === 'compact' ? 'py-0' : density === 'spacious' ? 'py-2' : 'py-1'}`}>
              <span className={`text-gray-500 truncate ${density === 'compact' ? 'text-sm' : density === 'spacious' ? 'text-base' : 'text-sm'}`}>
              {category.description || 'No description'}
            </span>
          </td>
          )}

          {visibleColumns.services && (
            <td className={`px-6 whitespace-nowrap ${density === 'compact' ? 'py-0' : density === 'spacious' ? 'py-2' : 'py-1'}`}>
              <span className={`text-gray-500 truncate ${density === 'compact' ? 'text-sm' : density === 'spacious' ? 'text-base' : 'text-sm'}`}>
              {category._count && typeof category._count.services === 'number' 
                ? category._count.services 
                : '0'}
            </span>
          </td>
          )}

          {visibleColumns.status && (
            <td className={`px-6 whitespace-nowrap ${density === 'compact' ? 'py-0' : density === 'spacious' ? 'py-2' : 'py-1'}`}>
              <span className={`text-gray-500 truncate ${density === 'compact' ? 'text-sm' : density === 'spacious' ? 'text-base' : 'text-sm'}`}>
{category.isActive === true ? 'Active' : 'Inactive'}
            </span>
          </td>
          )}

          {visibleColumns.actions && (
            <td className={`px-6 whitespace-nowrap text-right font-medium ${density === 'compact' ? 'py-0 text-sm' : density === 'spacious' ? 'py-2 text-base' : 'py-1 text-sm'}`}>
              <div className={`flex items-center justify-start ${density === 'compact' ? 'space-x-2' : density === 'spacious' ? 'space-x-3' : 'space-x-2'}`}>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onEdit(category)
                }}
                className="text-blue-600 hover:text-blue-900 p-1"
                title="Edit category"
              >
                  <PencilIcon className={`${density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onToggleActive(category)
                }}
                className={`p-1 ${
                  category.isActive
                    ? 'text-green-600 hover:text-green-900'
                    : 'text-gray-400 hover:text-gray-600'
                }`}
                title={category.isActive ? 'Deactivate category' : 'Activate category'}
              >
                {category.isActive ? (
                    <EyeSlashIcon className={`${density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
                ) : (
                    <EyeIcon className={`${density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
                )}
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete(category)
                }}
                className="text-red-600 hover:text-red-900 p-1"
                title="Delete category"
              >
                  <TrashIcon className={`${density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
              </button>
            </div>
          </td>
          )}
        </tr>

        {/* Recursively render child categories */}
        {hasChildren && isExpanded && (
          <>
            {category.children?.map((child) => (
              <CategoryRow
                key={child.id}
                category={child}
                level={level + 1}
                isSelected={isSelected}
                onCategorySelect={onCategorySelect}
                onEdit={onEdit}
                onToggleActive={onToggleActive}
                onDelete={onDelete}
                expandedCategories={expandedCategories}
                onToggleExpanded={onToggleExpanded}
                isChecked={isChecked}
                onCheck={onCheck}
              />
            ))}
          </>
        )}
      </>
    )
  }

  const handleCreateClick = useCallback(() => {
    setIsFormOpen(true)
    resetForm()
    showInfo('Create Category', 'Fill out the form below to create a new category.')
  }, [resetForm, showInfo])

  const handleFiltersChange = useCallback((newFilters: Record<string, string>) => {
    setCurrentFilters(newFilters)
  }, [])

  const handleDensityChange = useCallback((newDensity: Density) => {
    setDensity(newDensity)
    setVisibleColumns(getDefaultColumns(newDensity))
  }, [getDefaultColumns])

  // Flatten hierarchical category tree for grid view
  const getAllCategories = useCallback((cats: Category[]): Category[] => {
    let all: Category[] = []
    cats.forEach(cat => {
      all.push(cat)
      if (cat.children && cat.children.length > 0) {
        all = all.concat(getAllCategories(cat.children))
      }
    })
    return all
  }, [])

  // Bulk Operations
  // Activate multiple selected categories
  const handleBulkActivate = useCallback(async () => {
    try {
      showLoading('Activating Categories', `Activating ${selectedCategories.length} categories...`)
      
      const promises = selectedCategories.map(categoryId => {
        const category = getAllCategories(categories).find(cat => cat.id === categoryId)
        if (category && !category.isActive) {
          return fetch(`/api/admin/categories/${categoryId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              categname: category.name,
              categdesc: category.description,
              parentid: category.parentId ? Number(category.parentId) : 0,
              isactive: true,
              displayorder: category.displayOrder
            })
          })
        }
        return Promise.resolve()
      })

      await Promise.all(promises)
      showSuccess('Categories Activated', `Successfully activated ${selectedCategories.length} categories.`)
      await fetchCategories(false)
      setSelectedCategories([])
      setShowBulkActions(false)
    } catch (error) {
      console.error('Error activating categories:', error)
      showError('Activation Error', 'Failed to activate selected categories.')
    }
  }, [selectedCategories, getAllCategories, categories, showLoading, showSuccess, fetchCategories, showError])

  // Deactivate multiple selected categories
  const handleBulkDeactivate = useCallback(async () => {
    try {
      showLoading('Deactivating Categories', `Deactivating ${selectedCategories.length} categories...`)
      
      const promises = selectedCategories.map(categoryId => {
        const category = getAllCategories(categories).find(cat => cat.id === categoryId)
        if (category && category.isActive) {
          return fetch(`/api/admin/categories/${categoryId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              categname: category.name,
              categdesc: category.description,
              parentid: category.parentId ? Number(category.parentId) : 0,
              isactive: false,
              displayorder: category.displayOrder
            })
          })
        }
        return Promise.resolve()
      })

      await Promise.all(promises)
      showSuccess('Categories Deactivated', `Successfully deactivated ${selectedCategories.length} categories.`)
      await fetchCategories(false)
      setSelectedCategories([])
      setShowBulkActions(false)
    } catch (error) {
      console.error('Error deactivating categories:', error)
      showError('Deactivation Error', 'Failed to deactivate selected categories.')
    }
  }, [selectedCategories, getAllCategories, categories, showLoading, showSuccess, fetchCategories, showError])

  // Show confirmation modal for bulk delete with dependency verification
  const showBulkDeleteConfirmation = useCallback(async (categoryIds: string[]) => {
    const count = categoryIds.length
    const categoriesToDelete = getAllCategories(categories).filter(cat => 
      categoryIds.includes(cat.id)
    )
    
    try {
      showLoading('Verifying Categories', `Checking dependencies for ${categoriesToDelete.length} categories...`)
      
      // Check dependencies for each category
      const verificationPromises = categoriesToDelete.map(async (category) => {
        try {
          const response = await fetch(`/api/admin/categories/${category.id}`)
          if (response.ok) {
            const categoryData = await response.json()
            const freshCategory = categoryData.data
            
            const subcategoriesCount = freshCategory._count?.children || 0
            const servicesCount = freshCategory._count?.services || 0
            
            return {
              category,
              canDelete: subcategoriesCount === 0 && servicesCount === 0,
              subcategories: subcategoriesCount,
              services: servicesCount,
              dependencies: [
                ...(subcategoriesCount > 0 ? [`${subcategoriesCount} subcategor${subcategoriesCount === 1 ? 'y' : 'ies'}`] : []),
                ...(servicesCount > 0 ? [`${servicesCount} service${servicesCount === 1 ? '' : 's'}`] : [])
              ]
            }
          }
          return {
            category,
            canDelete: false,
            subcategories: 0,
            services: 0,
            dependencies: ['Unable to verify dependencies']
          }
        } catch (error) {
          return {
            category,
            canDelete: false,
            subcategories: 0,
            services: 0,
            dependencies: ['Verification failed']
          }
        }
      })
      
      const verificationResults = await Promise.all(verificationPromises)
      
      // Calculate overall verification data
      const totalSubcategories = verificationResults.reduce((sum, result) => sum + result.subcategories, 0)
      const totalServices = verificationResults.reduce((sum, result) => sum + result.services, 0)
      const canDeleteAll = verificationResults.every(result => result.canDelete)
      const safeToDelete = verificationResults.filter(result => result.canDelete)
      const unsafeToDelete = verificationResults.filter(result => !result.canDelete)
      
      let reason = ''
      let dependencies: string[] = []
      
      if (canDeleteAll) {
        reason = 'All selected categories are safe to delete'
        dependencies = []
      } else {
        reason = `${unsafeToDelete.length} of ${categoriesToDelete.length} categories have dependencies`
        dependencies = [
          `${totalSubcategories} total subcategor${totalSubcategories === 1 ? 'y' : 'ies'}`,
          `${totalServices} total service${totalServices === 1 ? '' : 's'}`
        ]
      }
      
      setDeleteConfirmation({ 
        isOpen: true, 
        category: null,
        isBulkDelete: true,
        bulkCategories: categoriesToDelete,
        verificationData: {
          canDelete: canDeleteAll,
          reason,
          subcategories: totalSubcategories,
          services: totalServices,
          dependencies
        },
        showVerification: true
      })
      
    } catch (error) {
      console.error('Error verifying bulk delete:', error)
      showError('Verification Error', 'Failed to verify category dependencies.')
    }
  }, [selectedCategories, getAllCategories, categories, showLoading, showError])

  // Clear all selected categories
  const handleClearSelection = useCallback(() => {
    setSelectedCategories([])
    setShowBulkActions(false)
  }, [])

  // Handle column sorting
  const handleSort = useCallback((field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }, [sortField, sortDirection])

  // Get sort icon based on current sort state
  const getSortIcon = useCallback((field: string) => {
    if (sortField !== field) {
      return <ArrowUpIcon className="h-3 w-3 text-gray-300" />
    }
    return sortDirection === 'asc' 
      ? <ArrowUpIcon className="h-3 w-3 text-blue-600" />
      : <ArrowDownIcon className="h-3 w-3 text-blue-600" />
  }, [sortField, sortDirection])

  // Card Rendering
  // Render individual category card with hover effects and action menu
  const renderCategoryCard = (category: Category, isLargeCard: boolean = false) => {
    const isSelected = selectedCategory?.id === category.id
    const hasChildren = category.children && category.children.length > 0

    // Apply density-based padding
    const getDensityPadding = () => {
      if (isLargeCard) return 'p-5'
      switch (density) {
        case 'compact': return 'p-3'
        case 'comfortable': return 'p-4'
        case 'spacious': return 'p-5'
        default: return 'p-4'
      }
    }

    return (
      <div
        key={category.id}
        className={`group relative bg-white border border-gray-200 rounded-lg cursor-pointer overflow-hidden hover:cursor-pointer shadow-sm hover:shadow-md ${
          isSelected
            ? 'ring-2 ring-blue-500 ring-offset-2 bg-blue-50/50 border-blue-300'
            : 'hover:bg-gray-50/50 hover:border-gray-300'
        } ${
          selectedCategories.includes(category.id) 
            ? 'bg-blue-50 border-l-4 border-blue-500' 
            : ''
        } ${getDensityPadding()}`}
        onClick={() => onCategorySelect(category)}
        onMouseEnter={(e) => {
          // Only show action menu on hover for desktop (lg and up)
          if (window.innerWidth >= 1024) {
            const actionMenu = e.currentTarget.querySelector('.action-menu') as HTMLElement;
            if (actionMenu) {
              actionMenu.style.opacity = '1';
              actionMenu.style.transform = 'translateX(0)';
              actionMenu.style.pointerEvents = 'auto';
            }
          }
        }}
        onMouseLeave={(e) => {
          // Only hide action menu on mouse leave for desktop (lg and up)
          if (window.innerWidth >= 1024) {
            const actionMenu = e.currentTarget.querySelector('.action-menu') as HTMLElement;
            if (actionMenu) {
              actionMenu.style.opacity = '0';
              actionMenu.style.transform = 'translateX(100%)';
              actionMenu.style.pointerEvents = 'none';
            }
          }
        }}
      >
        {/* Header Section */}
        <div className={`flex items-start justify-between ${density === 'compact' ? 'mb-1' : density === 'spacious' ? 'mb-3' : 'mb-2'}`}>
          <div className={`flex items-start flex-1 min-w-0 ${density === 'compact' ? 'space-x-1' : density === 'spacious' ? 'space-x-3' : 'space-x-2'}`}>
            {/* Checkbox */}
            <div className="flex-shrink-0 pt-1" onClick={(e) => e.stopPropagation()}>
              <input
                type="checkbox"
                checked={selectedCategories.includes(category.id)}
                onChange={(e) => {
                  e.stopPropagation()
                  handleSelectCategory(category.id)
                }}
                onClick={(e) => e.stopPropagation()}
                className={`h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${
                  density === 'compact' 
                    ? 'h-3 w-3' 
                    : density === 'spacious' 
                    ? 'h-5 w-5' 
                    : 'h-4 w-4'
                }`}
              />
            </div>
            {/* Icon Container */}
            <div className={`flex-shrink-0 bg-blue-50 rounded-lg ${density === 'compact' ? 'p-1.5' : density === 'spacious' ? 'p-3' : 'p-2'}`}>
              <i className={`fas ${getCategoryIcon(category)} ${density === 'compact' ? 'text-lg' : density === 'spacious' ? 'text-3xl' : 'text-2xl'}`}></i>
            </div>
            
            {/* Title and Description */}
            <div className="flex-1 min-w-0">
              <h3 className={`font-semibold text-gray-900 truncate ${density === 'compact' ? 'text-sm mb-1' : density === 'spacious' ? 'text-lg mb-3' : 'text-base mb-2'}`}>
                {category.name}
              </h3>
              {category.description && (
                <p className={`text-gray-600 line-clamp-2 leading-relaxed ${density === 'compact' ? 'text-xs' : density === 'spacious' ? 'text-base' : 'text-sm'}`}>
                  {category.description}
                </p>
              )}
            </div>
          </div>

                  {/* Status Badge */}
        <span className={`inline-flex items-center px-2.5 py-0 rounded-full text-xs font-medium ${
          category.isActive
            ? 'bg-green-100 text-green-800 border border-green-200'
            : 'bg-red-100 text-red-800 border border-red-200'
        }`}>
          <div className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
            category.isActive ? 'bg-green-400' : 'bg-red-400'
          }`}></div>
          {category.isActive ? 'Active' : 'Inactive'}
        </span>
        </div>

        {/* Divider */}
        <div className={`border-t border-gray-100 ${density === 'compact' ? 'my-0.5' : density === 'spacious' ? 'my-2' : 'my-1'}`}></div>

        {/* Info Grid */}
        <div className={`grid grid-cols-2 ${density === 'compact' ? 'gap-2 mb-0.5' : density === 'spacious' ? 'gap-6 mb-2' : 'gap-4 mb-1'}`}>
          {/* Parent Category */}
          <div className={`${density === 'compact' ? 'space-y-0' : density === 'spacious' ? 'space-y-1' : 'space-y-0.5'}`}>
            <div className={`font-medium text-gray-500 uppercase tracking-wide ${density === 'compact' ? 'text-xs' : density === 'spacious' ? 'text-sm' : 'text-xs'}`}>Parent</div>
            <div className={`font-medium text-gray-600 ${density === 'compact' ? 'text-xs' : density === 'spacious' ? 'text-base' : 'text-sm'}`}>
              {category.parentId ? 'Sub-category' : 'Main Category'}
            </div>
          </div>

          {/* Services Count */}
          <div className={`${density === 'compact' ? 'space-y-0' : density === 'spacious' ? 'space-y-1' : 'space-y-0.5'}`}>
            <div className={`font-medium text-gray-500 uppercase tracking-wide ${density === 'compact' ? 'text-xs' : density === 'spacious' ? 'text-sm' : 'text-xs'}`}>Services</div>
            <div className={`flex items-center ${density === 'compact' ? 'space-x-1' : density === 'spacious' ? 'space-x-3' : 'space-x-2'}`}>
              <span className={`inline-flex items-center bg-blue-100 text-blue-800 font-medium rounded-md ${density === 'compact' ? 'px-1 py-0 text-xs' : density === 'spacious' ? 'px-2 py-1 text-base' : 'px-1 py-0 text-sm'}`}>
                {category._count?.services || 0}
              </span>
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className={`flex items-center justify-between text-gray-500 border-t border-gray-100 ${density === 'compact' ? 'text-xs pt-0.5' : density === 'spacious' ? 'text-sm pt-2' : 'text-xs pt-1'}`}>
          <span className={`flex items-center ${density === 'compact' ? 'space-x-1' : density === 'spacious' ? 'space-x-2' : 'space-x-1'}`}>
            <span className="font-medium">Children:</span>
            <span className={`bg-gray-100 rounded ${density === 'compact' ? 'px-1 py-0' : density === 'spacious' ? 'px-2 py-1' : 'px-1 py-0'}`}>{category._count?.children || 0}</span>
          </span>
          <div className={`flex items-center ${density === 'compact' ? 'space-x-1' : density === 'spacious' ? 'space-x-2' : 'space-x-1'}`}>
            <span className={`flex items-center ${density === 'compact' ? 'space-x-1' : density === 'spacious' ? 'space-x-2' : 'space-x-1'}`}>
              <span className="font-medium">Order:</span>
              <span>{category.displayOrder}</span>
            </span>
            
            {/* Mobile Action Button - Always visible on mobile and small screens */}
            <div className="lg:hidden">
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  const actionMenu = e.currentTarget.closest('.group')?.querySelector('.action-menu') as HTMLElement;
                  if (actionMenu) {
                    const isVisible = actionMenu.style.opacity === '1';
                    actionMenu.style.opacity = isVisible ? '0' : '1';
                    actionMenu.style.transform = isVisible ? 'translateX(100%)' : 'translateX(0)';
                    actionMenu.style.pointerEvents = isVisible ? 'none' : 'auto';
                  }
                }}
                className="p-2 text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                title="Show Actions"
              >
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Actions Sidebar - Professional Overlay */}
        <div className={`action-menu absolute bg-white rounded-lg border border-gray-200 flex flex-col items-center justify-center transition-all duration-200 ${
          density === 'compact' 
            ? 'top-2 right-2 bottom-2 w-10 space-y-2' 
            : density === 'spacious' 
            ? 'top-4 right-4 bottom-4 w-14 space-y-6' 
            : 'top-3 right-3 bottom-3 w-12 space-y-4'
        }`} style={{
          opacity: '0',
          transform: 'translateX(100%)',
          pointerEvents: 'none'
        }}>
          {/* Edit Button */}
          <button
            onClick={(e) => {
              e.stopPropagation()
              handleEdit(category)
            }}
            className={`group/btn relative inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 border border-blue-500 hover:border-blue-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto ${
              density === 'compact' ? 'w-6 h-6' : density === 'spacious' ? 'w-10 h-10' : 'w-8 h-8'
            }`}
            title="Edit Category"
          >
            <PencilIcon className={`group-hover/btn:scale-110 ${density === 'compact' ? 'h-3 w-3' : density === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
          </button>
          
          {/* Toggle Active Button */}
          <button
            onClick={(e) => {
              e.stopPropagation()
              handleToggleActive(category)
            }}
            className={`group/btn relative inline-flex items-center justify-center border rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto ${
              density === 'compact' ? 'w-6 h-6' : density === 'spacious' ? 'w-10 h-10' : 'w-8 h-8'
            } ${
              category.isActive
                ? 'bg-green-600 hover:bg-green-700 border-green-500 hover:border-green-600 text-white focus:ring-green-500'
                : 'bg-orange-500 hover:bg-orange-600 border-orange-400 hover:border-orange-500 text-white focus:ring-orange-500'
            }`}
            title={category.isActive ? 'Deactivate Category' : 'Activate Category'}
          >
            {category.isActive ? (
              <EyeSlashIcon className={`group-hover/btn:scale-110 ${density === 'compact' ? 'h-3 w-3' : density === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
            ) : (
              <EyeIcon className={`group-hover/btn:scale-110 ${density === 'compact' ? 'h-3 w-3' : density === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
            )}
          </button>
          
          {/* Delete Button */}
          <button
            onClick={(e) => {
              e.stopPropagation()
              handleDelete(category)
            }}
            className={`group/btn relative inline-flex items-center justify-center bg-red-600 hover:bg-red-700 border border-red-500 hover:border-red-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto ${
              density === 'compact' ? 'w-6 h-6' : density === 'spacious' ? 'w-10 h-10' : 'w-8 h-8'
            }`}
            title="Delete Category"
          >
            <TrashIcon className={`group-hover/btn:scale-110 ${density === 'compact' ? 'h-3 w-3' : density === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
          </button>
        </div>
      </div>
    )
  }

  // Main Render
  return (
    <div className="space-y-3" style={{ paddingBottom: dropdownSpaceNeeded > 0 ? `${dropdownSpaceNeeded}px` : '0' }}>
      {/* Mobile Layout */}
      <div className="flex flex-col space-y-3 lg:hidden">
        {/* Search Bar - Full Width on Mobile */}
        <div className="w-full">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search categories..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
          </div>
        </div>

        {/* Mobile Controls - Single Row */}
        <div className="flex items-center gap-1">
          {/* View Mode Toggle - Stretched */}
          <div className="flex items-center bg-gray-100 rounded-lg p-0.5 flex-1">
            <button
              onClick={() => setViewMode('list')}
              className={`flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 ${
                viewMode === 'list'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              title="List view"
            >
              <ListBulletIcon className="h-3 w-3" />
              <span className="text-xs font-medium hidden xs:inline">List</span>
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 ${
                viewMode === 'grid'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              title="Grid view"
            >
              <Squares2X2Icon className="h-3 w-3" />
              <span className="text-xs font-medium hidden xs:inline">Grid</span>
            </button>
          </div>

          {/* Grid Columns Control (for grid view) */}
          {viewMode === 'grid' && (
            <div className="flex items-center bg-gray-100 rounded-lg p-0.5 flex-1">
              <span className="text-xs font-medium text-gray-700 px-1">Col:</span>
              <div className="flex items-center gap-0.5 flex-1">
                {[1, 2, 3, 4].map((num) => (
                  <button
                    key={num}
                    onClick={() => setGridColumns(num)}
                    className={`flex-1 px-1.5 py-1 rounded text-xs font-medium ${
                      gridColumns === num
                        ? 'bg-white text-green-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                    title={`${num} column${num > 1 ? 's' : ''}`}
                  >
                    {num}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Table Columns Control (for list view) */}
          {viewMode === 'list' && (
            <div className="relative flex-1">
              <button
                onClick={() => setShowColumnSelector(!showColumnSelector)}
                className="w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                title="Columns"
              >
                <ViewColumnsIcon className="h-3 w-3 mr-0.5" />
                <span className="hidden xs:inline">Col</span>
                <ChevronDownIcon className="h-3 w-3 ml-0.5" />
              </button>
            </div>
          )}

          {/* Filters Button - Stretched */}
          <div className="relative flex-1">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium rounded-lg border ${
                showFilters || Object.keys(currentFilters).some(key => currentFilters[key])
                  ? 'bg-blue-50 text-blue-700 border-blue-300'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              title="Filters"
            >
              <FunnelIcon className="h-3 w-3 mr-0.5" />
              <span className="hidden xs:inline">Filter</span>
              {Object.keys(currentFilters).some(key => currentFilters[key]) && (
                <span className="ml-0.5 inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {Object.values(currentFilters).filter(Boolean).length}
                </span>
              )}
            </button>
          </div>

          {/* Density Control - Stretched */}
          <div className="relative flex-1">
            <button
              onClick={() => setShowWindowList(!showWindowList)}
              className="w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
              title="Density"
            >
              <AdjustmentsHorizontalIcon className="h-3 w-3 mr-0.5" />
              <span className="hidden xs:inline">{density.charAt(0).toUpperCase() + density.slice(1)}</span>
              <ChevronDownIcon className="h-3 w-3 ml-0.5" />
            </button>
          </div>

          {/* Create Button - Stretched */}
          <button
            onClick={() => setIsFormOpen(true)}
            className="flex-1 inline-flex items-center justify-center px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="h-3 w-3 mr-0.5" />
            <span className="hidden xs:inline">Add</span>
          </button>
        </div>
      </div>

      {/* Desktop Layout - Horizontal */}
      <div className="hidden lg:flex items-center justify-between gap-4 mb-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm">
        {/* Search Bar and Filters */}
        <div className="flex items-center gap-3 flex-1 max-w-md">
          <div className="relative flex-1">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search categories by name or description..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
          </div>

          {/* Filters Button */}
          <div className="relative dropdown-container">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                showFilters || Object.keys(currentFilters).some(key => currentFilters[key])
                  ? 'bg-blue-50 text-blue-700 border-blue-300'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              title="Show/hide filters"
            >
              <FunnelIcon className="h-4 w-4 mr-2" />
              Filters
              {Object.keys(currentFilters).some(key => currentFilters[key]) && (
                <span className="ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {Object.values(currentFilters).filter(Boolean).length}
                </span>
              )}
              <ChevronDownIcon className="h-4 w-4 ml-2" />
            </button>

            {/* Filters Dropdown */}
            {showFilters && (
              <div className="hidden lg:block absolute top-full right-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                <div className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-medium text-gray-900">Filters</h3>
                    <button
                      onClick={() => setShowFilters(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <XMarkIcon className="h-4 w-4" />
                    </button>
                  </div>
                  
                  <div className="space-y-4">
                    {filters.map((filter) => (
                      <div key={filter.key}>
                        <label className="block text-xs font-medium text-gray-700 mb-2">
                          {filter.label}
                        </label>
                        <select
                          value={currentFilters[filter.key] || ''}
                          onChange={(e) => {
                            const newFilters = { ...currentFilters }
                            if (e.target.value) {
                              newFilters[filter.key] = e.target.value
                            } else {
                              delete newFilters[filter.key]
                            }
                            setCurrentFilters(newFilters)
                          }}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          {filter.options.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex justify-end mt-4 pt-4 border-t border-gray-200">
                    <button
                      onClick={() => {
                        setCurrentFilters({})
                        setShowFilters(false)
                      }}
                      className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg"
                    >
                      Clear All
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* View Options and Controls */}
        <div className="flex items-center gap-3">
          {/* View Mode Toggle */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">View:</span>
            <div className="flex items-center bg-gray-100 rounded-lg p-1 gap-1">
              <button
                onClick={() => setViewMode('list')}
                className={`px-3 py-2 rounded-md flex items-center gap-2 ${
                  viewMode === 'list'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="List view"
              >
                <ListBulletIcon className="h-5 w-5" />
                <span className="text-sm font-medium">List</span>
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`px-3 py-2 rounded-md flex items-center gap-2 ${
                  viewMode === 'grid'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Grid view"
              >
                <Squares2X2Icon className="h-5 w-5" />
                <span className="text-sm font-medium">Grid</span>
              </button>
            </div>
          </div>

          {/* Grid Columns Control (for grid view) */}
          {viewMode === 'grid' && (
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700">Columns:</span>
              <div className="flex items-center bg-gray-100 rounded-lg p-1 gap-1">
                <button
                  onClick={() => setGridColumns(1)}
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    gridColumns === 1
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="1 column"
                >
                  1
                </button>
                <button
                  onClick={() => setGridColumns(2)}
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    gridColumns === 2
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="2 columns"
                >
                  2
                </button>
                <button
                  onClick={() => setGridColumns(3)}
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    gridColumns === 3
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="3 columns"
                >
                  3
                </button>
                <button
                  onClick={() => setGridColumns(4)}
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    gridColumns === 4
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="4 columns"
                >
                  4
                </button>
              </div>
            </div>
          )}

          {/* Table Columns Control (for list view) */}
          {viewMode === 'list' && (
            <div className="relative dropdown-container">
              <button
                onClick={() => setShowColumnSelector(!showColumnSelector)}
                className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                title="Select columns to display"
              >
                <ViewColumnsIcon className="h-4 w-4 mr-2" />
                Columns
                <ChevronDownIcon className="h-4 w-4 ml-2" />
              </button>

              {/* Column Selector Dropdown */}
              {showColumnSelector && (
                <div className="hidden lg:block absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                  <div className="p-2">
                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Show Columns</div>
                    {Object.entries(visibleColumns).map(([key, visible]) => (
                      <label key={key} className="flex items-center space-x-2 py-1">
                        <input
                          type="checkbox"
                          checked={visible}
                          onChange={(e) => setVisibleColumns(prev => ({ ...prev, [key]: e.target.checked }))}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="text-sm text-gray-700 capitalize">{key}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Density Dropdown */}
          <div className="relative dropdown-container">
            <button
              onClick={() => setShowWindowList(!showWindowList)}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              title="Select density"
            >
              <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
              {density.charAt(0).toUpperCase() + density.slice(1)}
              <ChevronDownIcon className="h-4 w-4 ml-2" />
            </button>

            {/* Density Dropdown */}
            {showWindowList && (
              <div className="hidden lg:block absolute top-full left-0 mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                <div className="p-1">
                  {(['compact', 'comfortable', 'spacious'] as const).map((option) => (
                    <button
                      key={option}
                      onClick={() => {
                        handleDensityChange(option)
                        setShowWindowList(false)
                      }}
                      className={`w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 ${
                        density === option ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'
                      }`}
                    >
                      {option.charAt(0).toUpperCase() + option.slice(1)}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>


          {/* Create Button */}
          <button
            onClick={handleCreateClick}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Category
          </button>
        </div>
      </div>

      {/* Mobile Dropdowns */}
      {/* Filters Dropdown - Mobile */}
      {showFilters && (
        <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-900">Filters</h3>
            <button
              onClick={() => setShowFilters(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <select
                value={currentFilters.status || ''}
                onChange={(e) => {
                  const newFilters = { ...currentFilters }
                  if (e.target.value) {
                    newFilters.status = e.target.value
                  } else {
                    delete newFilters.status
                  }
                  setCurrentFilters(newFilters)
                }}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
              <select
                value={currentFilters.sortBy || ''}
                onChange={(e) => {
                  const newFilters = { ...currentFilters }
                  if (e.target.value) {
                    newFilters.sortBy = e.target.value
                  } else {
                    delete newFilters.sortBy
                  }
                  setCurrentFilters(newFilters)
                }}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Default</option>
                <option value="name">Name</option>
                <option value="createdAt">Created Date</option>
                <option value="updatedAt">Updated Date</option>
              </select>
            </div>
          </div>
          
          <div className="flex justify-end mt-4 pt-4 border-t border-gray-200">
            <button
              onClick={() => {
                setCurrentFilters({})
                setShowFilters(false)
              }}
              className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg"
            >
              Clear All
            </button>
          </div>
        </div>
      )}

      {/* Column Selector Dropdown - Mobile */}
      {showColumnSelector && (
        <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4">
          <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Show Columns</div>
          {Object.entries(visibleColumns).map(([key, visible]) => (
            <label key={key} className="flex items-center space-x-2 py-1">
              <input
                type="checkbox"
                checked={visible}
                onChange={(e) => setVisibleColumns(prev => ({ ...prev, [key]: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700 capitalize">{key}</span>
            </label>
          ))}
        </div>
      )}

      {/* Density Dropdown - Mobile */}
      {showWindowList && (
        <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-2">
          {(['compact', 'comfortable', 'spacious'] as const).map((option) => (
            <button
              key={option}
              onClick={() => {
                setDensity(option)
                setShowWindowList(false)
              }}
              className={`w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 ${
                density === option ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'
              }`}
            >
              {option.charAt(0).toUpperCase() + option.slice(1)}
            </button>
          ))}
        </div>
      )}

      {/* Categories Content */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading categories...</p>
          </div>
        ) : filteredCategories.length === 0 ? (
          <div className="p-6 text-center">
            <FolderIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No categories found</h3>
            <p className="text-gray-600 mb-4">
              {searchQuery || Object.keys(currentFilters).some(key => currentFilters[key]) 
                ? 'Try adjusting your search terms or filters.' 
                : 'Get started by creating your first category.'}
            </p>
            <button
              onClick={handleCreateClick}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Category
            </button>
          </div>
        ) : (
          <div>
            {viewMode === 'list' && (
              <div className="overflow-hidden">
                {/* Bulk Actions Bar - Shows when categories are selected */}
                {showBulkActions && selectedCategories.length > 0 && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg px-4 py-2 shadow-sm">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-blue-600 font-semibold text-xs">
                              {selectedCategories.length}
                            </span>
                          </div>
                          <span className="text-xs font-medium text-blue-900">
                            {selectedCategories.length === 1 ? 'category' : 'categories'} selected
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-1.5">
                          <button
                            onClick={handleBulkActivate}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 border border-green-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                            title="Activate selected categories"
                          >
                            <EyeIcon className="h-3 w-3 mr-1" />
                            Activate
                          </button>
                          
                          <button
                            onClick={handleBulkDeactivate}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500"
                            title="Deactivate selected categories"
                          >
                            <EyeSlashIcon className="h-3 w-3 mr-1" />
                            Deactivate
                          </button>
                          
                          <button
                            onClick={() => showBulkDeleteConfirmation(selectedCategories)}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 border border-red-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500"
                            title="Delete selected categories"
                          >
                            <TrashIcon className="h-3 w-3 mr-1" />
                            Delete
                          </button>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-1.5">
                        <button
                          onClick={handleClearSelection}
                          className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gray-500"
                          title="Clear selection"
                        >
                          <XMarkIcon className="h-3 w-3 mr-1" />
                          Clear
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                <div className={showBulkActions && selectedCategories.length > 0 ? "mt-3" : ""}>
                  <table key={categoriesKey} className="min-w-full">
                  <thead className="bg-gray-200 border-b border-gray-300">
                    <tr>
                      <th scope="col" className="relative pl-2 py-2" style={{ width: '6px' }}>
                        <input
                          type="checkbox"
                          checked={selectedCategories.length === filteredCategories.length && filteredCategories.length > 0}
                          onChange={handleSelectAll}
                          className={`text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${
                            density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'
                          }`}
                        />
                      </th>
                      {visibleColumns.name && (
                        <th scope="col" className="pl-6 pr-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-24 py-2 text-xs" onClick={() => handleSort('name')}>
                          <div className="flex items-center space-x-1">
                            <span>Category</span>
                            {getSortIcon('name')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.description && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-80 py-2 text-xs" onClick={() => handleSort('description')}>
                          <div className="flex items-center space-x-1">
                            <span>Description</span>
                            {getSortIcon('description')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.services && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('services')}>
                          <div className="flex items-center space-x-1">
                            <span>Services</span>
                            {getSortIcon('services')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.status && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('isActive')}>
                          <div className="flex items-center space-x-1">
                            <span>Status</span>
                            {getSortIcon('isActive')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.actions && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider py-2 text-xs">
                          <span>Actions</span>
                      </th>
                      )}
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                    {paginationData.paginatedCategories.map((category) => (
                      <CategoryRow
                        key={category.id}
                        category={category}
                        level={0}
                        isSelected={selectedCategory?.id === category.id}
                        onCategorySelect={onCategorySelect}
                        onEdit={handleEdit}
                        onToggleActive={handleToggleActive}
                        onDelete={handleDelete}
                        expandedCategories={expandedCategories}
                        onToggleExpanded={toggleExpanded}
                        isChecked={selectedCategories.includes(category.id)}
                        onCheck={handleSelectCategory}
                      />
                    ))}
                  </tbody>
                </table>
                </div>
              </div>
            )}

            {viewMode === 'grid' && (
              <div>
                {/* Bulk Actions Bar for Grid View */}
                {showBulkActions && selectedCategories.length > 0 && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg px-4 py-2 shadow-sm">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-blue-600 font-semibold text-xs">
                              {selectedCategories.length}
                            </span>
                          </div>
                          <span className="text-xs font-medium text-blue-900">
                            categor{selectedCategories.length === 1 ? 'y' : 'ies'} selected
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-1.5">
                          <button
                            onClick={handleBulkActivate}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 border border-green-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                            title="Activate selected categories"
                          >
                            <EyeIcon className="h-3 w-3 mr-1" />
                            Activate
                          </button>
                          
                          <button
                            onClick={handleBulkDeactivate}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500"
                            title="Deactivate selected categories"
                          >
                            <EyeSlashIcon className="h-3 w-3 mr-1" />
                            Deactivate
                          </button>
                          
                          <button
                            onClick={() => showBulkDeleteConfirmation(selectedCategories)}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 border border-red-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500"
                            title="Delete selected categories"
                          >
                            <TrashIcon className="h-3 w-3 mr-1" />
                            Delete
                          </button>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-1.5">
                        <button
                          onClick={handleClearSelection}
                          className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gray-500"
                          title="Clear selection"
                        >
                          <XMarkIcon className="h-3 w-3 mr-1" />
                          Clear
                        </button>
                      </div>
                    </div>
                  </div>
                )}
                
                <div className={showBulkActions && selectedCategories.length > 0 ? "mt-3" : ""}>
                  <div key={categoriesKey} className={`grid gap-4 ${
                  gridColumns === 1 ? 'grid-cols-1' :
                  gridColumns === 2 ? 'grid-cols-1 md:grid-cols-2' :
                  gridColumns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
                  'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                }`}>
                    {getAllCategories(paginationData.paginatedCategories).map((category) => renderCategoryCard(category, false))}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Responsive Pagination */}
      {filteredCategories.length > 0 && (
        <ResponsivePagination
          currentPage={currentPage}
          totalPages={paginationData.totalPages}
          onPageChange={handlePageChange}
          itemsPerPage={itemsPerPage}
          onItemsPerPageChange={handleItemsPerPageChange}
          totalItems={filteredCategories.length}
          startIndex={paginationData.startIndex}
          endIndex={paginationData.endIndex}
          itemsPerPageOptions={[5, 10, 20, 50]}
          showItemsPerPage={true}
          showPageInfo={true}
        />
      )}

      {/* Form Modal */}
      <CategoryFormModal
        isOpen={isFormOpen}
        onClose={() => {
          setIsFormOpen(false)
          if (editingCategory) {
            showInfo('Edit Cancelled', `Editing "${editingCategory.name}" was cancelled.`)
          } else {
            showInfo('Create Cancelled', 'Category creation was cancelled.')
          }
        }}
        onSubmit={async (formData) => {
          try {
            const url = editingCategory 
              ? `/api/admin/categories/${editingCategory.id}`
              : '/api/admin/categories'
            
            const method = editingCategory ? 'PUT' : 'POST'
            
            const requestBody = {
              categname: formData.name,
              categdesc: formData.description,
              parentid: formData.parentId ? Number(formData.parentId) : 0,
              isactive: formData.isActive,
              displayorder: formData.displayOrder
            }
            
            
            const response = await fetch(url, {
              method,
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(requestBody)
            })

            if (response.ok) {
              const result = await response.json()
              
              // Show success notification
              if (editingCategory) {
                showSuccess('Category Updated', `"${editingCategory.name}" has been successfully updated.`)
              } else {
                showSuccess('Category Created', `"${formData.name}" has been successfully created.`)
              }
              
              // Reset form after successful submission
              resetForm()
              await fetchCategories(false)
            } else {
              const errorData = await response.json()
              
              if (Object.keys(errorData).length === 0) {
                showError('Database Error', 'Database connection error. Please check if the database is running.')
              } else {
                showError('Save Failed', errorData.error || errorData.message || 'Failed to save category')
              }
            }
          } catch (error) {
            console.error('Error saving category:', error)
            showError('Save Error', 'An unexpected error occurred while saving the category')
          }
        }}
        editingCategory={editingCategory}
        categories={categories}
      />

      {/* Modals */}
      <ConfirmationModal
        isOpen={deleteConfirmation.isOpen}
        title={deleteConfirmation.isBulkDelete ? "Delete Categories" : "Delete Category"}
        message={(() => {
          if (deleteConfirmation.isBulkDelete) {
            const count = deleteConfirmation.bulkCategories?.length || 0
            if (deleteConfirmation.verificationData?.canDelete) {
              return count === 1 
                ? `Are you sure you want to delete "${deleteConfirmation.bulkCategories?.[0]?.name}"?`
                : `Are you sure you want to delete ${count} categories?`
            } else {
              return count === 1 
                ? `Cannot delete "${deleteConfirmation.bulkCategories?.[0]?.name}"`
                : `Cannot delete ${count} categories`
            }
          }
          
          if (!deleteConfirmation.verificationData) return `Are you sure you want to delete "${deleteConfirmation.category?.name}"?`
          
          if (deleteConfirmation.verificationData.canDelete) {
            const hasSubcategories = (deleteConfirmation.verificationData.subcategories || 0) > 0
            const hasServices = (deleteConfirmation.verificationData.services || 0) > 0
            
            if (hasSubcategories && hasServices) {
              return `Delete "${deleteConfirmation.category?.name}" and all its data?`
            } else if (hasSubcategories) {
              return `Delete "${deleteConfirmation.category?.name}" and its subcategories?`
            } else if (hasServices) {
              return `Delete "${deleteConfirmation.category?.name}" and its services?`
            } else {
              return `Delete "${deleteConfirmation.category?.name}"?`
            }
          } else {
            return `Cannot delete "${deleteConfirmation.category?.name}"`
          }
        })()}
        details={(() => {
          if (deleteConfirmation.isBulkDelete) {
            const count = deleteConfirmation.bulkCategories?.length || 0
            if (deleteConfirmation.verificationData?.canDelete) {
              return `This will permanently remove ${count} ${count === 1 ? 'category' : 'categories'} and all associated data. This action cannot be undone.`
            } else {
              const totalSubcategories = deleteConfirmation.verificationData?.subcategories || 0
              const totalServices = deleteConfirmation.verificationData?.services || 0
              return `Some categories have dependencies. Only categories without subcategories or services will be deleted. This will skip ${totalSubcategories + totalServices} dependencies.`
            }
          }
          
          if (!deleteConfirmation.verificationData) return "This action cannot be undone. All associated data will be permanently removed."
          
          if (deleteConfirmation.verificationData.canDelete) {
            const hasSubcategories = (deleteConfirmation.verificationData.subcategories || 0) > 0
            const hasServices = (deleteConfirmation.verificationData.services || 0) > 0
            
            if (hasSubcategories && hasServices) {
              return "This will permanently remove the category, all its subcategories, and services. This action cannot be undone."
            } else if (hasSubcategories) {
              return "This will permanently remove the category and all its subcategories. This action cannot be undone."
            } else if (hasServices) {
              return "This will permanently remove the category and all its services. This action cannot be undone."
            } else {
              return "This category has no dependencies. It will be permanently removed. This action cannot be undone."
            }
          } else {
            return "Please remove all dependencies before attempting to delete this category."
          }
        })()}
        confirmText={deleteConfirmation.verificationData?.canDelete || deleteConfirmation.isBulkDelete ? "Delete" : "Delete Safe Only"}
        cancelText="Cancel"
        onConfirm={confirmDelete}
        onCancel={cancelDelete}
        type={deleteConfirmation.verificationData?.canDelete || deleteConfirmation.isBulkDelete ? 'danger' : 'verification'}
        verificationData={deleteConfirmation.verificationData ? {
          canDelete: deleteConfirmation.verificationData.canDelete,
          reason: deleteConfirmation.verificationData.reason,
          dependencies: deleteConfirmation.verificationData.dependencies
        } : undefined}
        showVerification={deleteConfirmation.showVerification}
      />

    </div>
  )
})

CategoryManagement.displayName = 'CategoryManagement'
