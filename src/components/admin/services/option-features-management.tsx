'use client'

// Imports
import { useState, useEffect, useCallback, useMemo, memo } from 'react'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  StarIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  CheckIcon,
  XMarkIcon,
  ListBulletIcon,
  Squares2X2Icon,
  ChevronDownIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ViewColumnsIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline'
import { ConfirmationModal } from '../shared/confirmation-modal'
import { ResponsivePagination } from '../shared/responsive-pagination'
import { FeatureFormModal } from './feature-form-modal'

// Type Definitions
interface ServiceOption {
  id: string
  serviceId: string
  name: string
  description?: string
  price?: number
  discountRate?: number
  totalDiscount?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  service?: {
    id: string
    name: string
    category?: {
      id: string
      name: string
    }
  }
  _count?: {
    features: number
    orderDetails: number
  }
}

interface OptionFeature {
  id: string
  optionId: string
  name: string
  description?: string
  price?: number
  discountRate?: number
  totalDiscount?: number
  isIncluded: boolean
  createdAt: string
  updatedAt: string
  option?: {
    id: string
    name: string
  }
}

interface OptionFeaturesManagementProps {
  option: ServiceOption
  showSuccess: (title: string, message: string) => void
  showError: (title: string, message: string) => void
  showWarning: (title: string, message: string) => void
  showInfo: (title: string, message: string) => void
  showLoading: (title: string, message: string) => string
  clearLoadingNotifications: () => void
}

interface VerificationData {
  canDelete: boolean
  reason?: string
  orderDetails?: number
  dependencies?: string[]
}

interface Filters {
  status?: string
  price?: string
  type?: string
  [key: string]: string | undefined
}

interface VisibleColumns {
  name: boolean
  description: boolean
  price: boolean
  discountRate: boolean
  totalDiscount: boolean
  status: boolean
  actions: boolean
}

// Constants
// Default column visibility configuration for list view
const DEFAULT_VISIBLE_COLUMNS: VisibleColumns = {
  name: true,
  description: true,
  price: true,
  discountRate: true,
  totalDiscount: true,
  status: true,
  actions: true
}

// Feature icon mapping based on feature name keywords - provides visual categorization
const FEATURE_ICON_MAP = {
  design: 'fa-palette text-purple-500',
  content: 'fa-file-alt text-blue-500',
  ecommerce: 'fa-shopping-cart text-orange-500',
  seo: 'fa-search text-green-500',
  security: 'fa-shield-alt text-red-500',
  performance: 'fa-tachometer-alt text-yellow-500',
  integration: 'fa-plug text-indigo-500',
  support: 'fa-life-ring text-teal-500',
  mobile: 'fa-mobile-alt text-pink-500',
  social: 'fa-share-alt text-blue-400',
  database: 'fa-database text-gray-500',
  default: 'fa-check-circle text-green-400',
  inactive: 'fa-circle text-gray-400'
} as const

// Main Component
export function OptionFeaturesManagement({ 
  option,
  showSuccess,
  showError,
  showWarning,
  showInfo,
  showLoading,
  clearLoadingNotifications
}: OptionFeaturesManagementProps) {
  // State Management
  // Core data state
  const [features, setFeatures] = useState<OptionFeature[]>([])
  const [filteredFeatures, setFilteredFeatures] = useState<OptionFeature[]>([])
  const [loading, setLoading] = useState(true)
  
  // Form and modal state
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingFeature, setEditingFeature] = useState<OptionFeature | null>(null)
  const [selectedFeature, setSelectedFeature] = useState<OptionFeature | null>(null)
  
  // Selection and bulk operations state
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([])
  const [showBulkActions, setShowBulkActions] = useState(false)
  
  // Search and filtering state
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [currentFilters, setCurrentFilters] = useState<Filters>({})
  
  // View and display state
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('grid')
  const [density, setDensity] = useState<'compact' | 'comfortable' | 'spacious'>('compact')
  const [gridColumns, setGridColumns] = useState<number>(3)
  const [visibleColumns, setVisibleColumns] = useState<VisibleColumns>(DEFAULT_VISIBLE_COLUMNS)
  
  // UI dropdown state
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnSelector, setShowColumnSelector] = useState(false)
  const [showWindowList, setShowWindowList] = useState(false)
  
  // Sorting state
  const [sortField, setSortField] = useState<string>('')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  
  // Delete confirmation state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [featureToDelete, setFeatureToDelete] = useState<OptionFeature | null>(null)
  const [isBulkDelete, setIsBulkDelete] = useState(false)
  const [bulkFeaturesToDelete, setBulkFeaturesToDelete] = useState<OptionFeature[]>([])
  const [verificationData, setVerificationData] = useState<VerificationData | null>(null)
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  
  // Utility Functions
  // Dynamic icon selection based on feature name keywords - provides visual categorization
  const getFeatureIcon = useCallback((feature: OptionFeature): string => {
    const name = feature.name.toLowerCase()

    const iconMappings = [
      { keywords: ['design', 'template', 'theme', 'layout'], icon: FEATURE_ICON_MAP.design },
      { keywords: ['page', 'content', 'blog', 'article'], icon: FEATURE_ICON_MAP.content },
      { keywords: ['product', 'cart', 'payment', 'checkout'], icon: FEATURE_ICON_MAP.ecommerce },
      { keywords: ['seo', 'meta', 'analytics', 'tracking'], icon: FEATURE_ICON_MAP.seo },
      { keywords: ['ssl', 'security', 'backup', 'protection'], icon: FEATURE_ICON_MAP.security },
      { keywords: ['speed', 'cache', 'optimization', 'performance'], icon: FEATURE_ICON_MAP.performance },
      { keywords: ['integration', 'api', 'plugin', 'extension'], icon: FEATURE_ICON_MAP.integration },
      { keywords: ['support', 'help', 'documentation', 'training'], icon: FEATURE_ICON_MAP.support },
      { keywords: ['mobile', 'responsive', 'app'], icon: FEATURE_ICON_MAP.mobile },
      { keywords: ['social', 'share', 'facebook', 'twitter'], icon: FEATURE_ICON_MAP.social },
      { keywords: ['database', 'storage', 'hosting', 'server'], icon: FEATURE_ICON_MAP.database }
    ]

    // Check for keyword matches and return appropriate icon
    for (const mapping of iconMappings) {
      if (mapping.keywords.some(keyword => name.includes(keyword))) {
        return mapping.icon
      }
    }

    // Fallback to status-based icon if no keyword match
    return feature.isIncluded ? FEATURE_ICON_MAP.default : FEATURE_ICON_MAP.inactive
  }, [])

  // Event Handlers
  // Handle density change and reset column visibility to defaults
  const handleDensityChange = useCallback((newDensity: 'compact' | 'comfortable' | 'spacious') => {
    setDensity(newDensity)
    setVisibleColumns(DEFAULT_VISIBLE_COLUMNS)
  }, [])

  // Toggle individual feature selection for bulk operations
  const handleSelectFeature = useCallback((featureId: string) => {
    setSelectedFeatures(prev => {
      const newSelection = prev.includes(featureId) 
        ? prev.filter(id => id !== featureId)
        : [...prev, featureId]
      setShowBulkActions(newSelection.length > 0)
      return newSelection
    })
  }, [])

  // Toggle select all features for bulk operations
  const handleSelectAll = useCallback(() => {
    if (selectedFeatures.length === filteredFeatures.length) {
      setSelectedFeatures([])
      setShowBulkActions(false)
    } else {
      setSelectedFeatures(filteredFeatures.map(feature => feature.id))
      setShowBulkActions(true)
    }
  }, [selectedFeatures.length, filteredFeatures])

  // Handle individual feature selection for detailed view
  const handleFeatureSelect = useCallback((feature: OptionFeature) => {
    setSelectedFeature(feature)
  }, [])

  // Bulk Operations
  // Bulk status update - only changes isIncluded flag, preserves other data
  const updateFeatureStatus = useCallback(async (featureIds: string[], isIncluded: boolean) => {
    const promises = featureIds.map(featureId => {
      const feature = features.find(f => f.id === featureId)
      if (!feature) return Promise.resolve()
      
      return fetch(`/api/admin/option-features/${featureId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: feature.name,
          description: feature.description,
          optionid: feature.optionId,
          price: 0, // Reset to 0 for status-only updates
          discountrate: 0,
          totaldiscount: 0,
          isincluded: isIncluded
        })
      })
    })
    
    await Promise.all(promises)
    
    // Update both features arrays to keep UI in sync
    const updateFeatures = (prev: OptionFeature[]) => 
      prev.map(feature => 
        featureIds.includes(feature.id) 
          ? { ...feature, isIncluded }
          : feature
      )
    
    setFeatures(updateFeatures)
    setFilteredFeatures(updateFeatures)
  }, [features])

  // Bulk activate selected features
  const handleBulkActivate = useCallback(async () => {
    if (selectedFeatures.length === 0) return
    
    try {
      showLoading('Activating Features', `Activating ${selectedFeatures.length} feature${selectedFeatures.length === 1 ? '' : 's'}...`)
      await updateFeatureStatus(selectedFeatures, true)
      showSuccess('Features Activated', `Successfully activated ${selectedFeatures.length} feature${selectedFeatures.length === 1 ? '' : 's'}.`)
      setSelectedFeatures([])
      setShowBulkActions(false)
    } catch (error) {
      console.error('Error activating features:', error)
      showError('Activation Failed', 'Failed to activate selected features')
    }
  }, [selectedFeatures, updateFeatureStatus, showLoading, showSuccess, showError])

  // Bulk deactivate selected features
  const handleBulkDeactivate = useCallback(async () => {
    if (selectedFeatures.length === 0) return
    
    try {
      showLoading('Deactivating Features', `Deactivating ${selectedFeatures.length} feature${selectedFeatures.length === 1 ? '' : 's'}...`)
      await updateFeatureStatus(selectedFeatures, false)
      showSuccess('Features Deactivated', `Successfully deactivated ${selectedFeatures.length} feature${selectedFeatures.length === 1 ? '' : 's'}.`)
      setSelectedFeatures([])
      setShowBulkActions(false)
    } catch (error) {
      console.error('Error deactivating features:', error)
      showError('Deactivation Failed', 'Failed to deactivate selected features')
    }
  }, [selectedFeatures, updateFeatureStatus, showLoading, showSuccess, showError])

  // Show confirmation modal for bulk delete with dependency verification
  const showBulkDeleteConfirmation = useCallback(async (featureIds: string[]) => {
    const count = featureIds.length
    const featuresToDelete = features.filter(feature => featureIds.includes(feature.id))
    
    try {
      showLoading('Verifying Features', `Checking dependencies for ${featuresToDelete.length} features...`)
      
      // Check dependencies for each feature
      const verificationPromises = featuresToDelete.map(async (feature) => {
        try {
          const response = await fetch(`/api/admin/option-features/${feature.id}`)
          if (response.ok) {
            const featureData = await response.json()
            const freshFeature = featureData.data
            
            const orderDetailsCount = freshFeature._count?.orderDetails || 0
            
            return {
              feature,
              canDelete: orderDetailsCount === 0,
              orderDetails: orderDetailsCount,
              dependencies: [
                ...(orderDetailsCount > 0 ? [`${orderDetailsCount} order${orderDetailsCount === 1 ? '' : 's'}`] : [])
              ]
            }
          }
          return {
            feature,
            canDelete: false,
            orderDetails: 0,
            dependencies: ['Unable to verify dependencies']
          }
        } catch (error) {
          return {
            feature,
            canDelete: false,
            orderDetails: 0,
            dependencies: ['Verification failed']
          }
        }
      })
      
      const verificationResults = await Promise.all(verificationPromises)
      
      // Calculate overall verification data
      const totalOrderDetails = verificationResults.reduce((sum, result) => sum + result.orderDetails, 0)
      const canDeleteAll = verificationResults.every(result => result.canDelete)
      const safeToDelete = verificationResults.filter(result => result.canDelete)
      const unsafeToDelete = verificationResults.filter(result => !result.canDelete)
      
      let reason = ''
      let dependencies: string[] = []
      
      if (canDeleteAll) {
        reason = 'All selected features are safe to delete'
        dependencies = []
      } else {
        reason = `${unsafeToDelete.length} of ${featuresToDelete.length} features have dependencies`
        dependencies = [
          `${totalOrderDetails} total order${totalOrderDetails === 1 ? '' : 's'}`
        ]
      }
      
      setBulkFeaturesToDelete(featuresToDelete)
      setIsBulkDelete(true)
      setVerificationData({
        canDelete: canDeleteAll,
        reason,
        orderDetails: totalOrderDetails,
        dependencies
      })
      setIsDeleteModalOpen(true)
      
    } catch (error) {
      console.error('Error verifying bulk delete:', error)
      showError('Verification Error', 'Failed to verify feature dependencies.')
    }
  }, [selectedFeatures, features, showLoading, showError])

  // Clear all selections and hide bulk actions
  const handleClearSelection = useCallback(() => {
    setSelectedFeatures([])
    setShowBulkActions(false)
  }, [])

  // Sorting and UI Controls
  // Handle column sorting with toggle between asc/desc
  const handleSort = useCallback((field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }, [sortField, sortDirection])

  // Get sort icon based on current sort state
  const getSortIcon = useCallback((field: string) => {
    if (sortField !== field) {
      return <ArrowUpIcon className="h-3 w-3 text-gray-300" />
    }
    return sortDirection === 'asc' 
      ? <ArrowUpIcon className="h-3 w-3 text-purple-600" />
      : <ArrowDownIcon className="h-3 w-3 text-purple-600" />
  }, [sortField, sortDirection])

  // Rendering Components
  // Memoized table row component for performance optimization
  const FeatureRow = memo(({ 
    feature, 
    isSelected, 
    onFeatureSelect, 
    onEdit, 
    onDelete,
    isChecked,
    onCheck,
    density,
    visibleColumns,
    getFeatureIcon
  }: {
    feature: OptionFeature
    isSelected: boolean
    onFeatureSelect: (feature: OptionFeature) => void
    onEdit: (feature: OptionFeature) => void
    onDelete: (feature: OptionFeature) => void
    isChecked: boolean
    onCheck: (featureId: string) => void
    density: 'compact' | 'comfortable' | 'spacious'
    visibleColumns: VisibleColumns
    getFeatureIcon: (feature: OptionFeature) => string
  }) => {

    return (
      <tr
        className={`group hover:bg-gray-50 cursor-pointer ${
          isChecked ? 'bg-purple-50 border-l-4 border-purple-500' : ''
        } ${
          density === 'compact' 
            ? 'py-0' 
            : density === 'spacious' 
            ? 'py-4' 
            : 'py-2'
        }`}
        onClick={() => onFeatureSelect(feature)}
      >
        <td className={`pl-2 whitespace-nowrap ${
          density === 'compact' 
            ? 'py-0' 
            : density === 'spacious' 
            ? 'py-4' 
            : 'py-2'
        }`} style={{ width: '6px' }}>
          <input
            type="checkbox"
            checked={isChecked}
            onChange={() => onCheck(feature.id)}
            onClick={(e) => e.stopPropagation()}
            className={`text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${
              density === 'compact' 
                ? 'h-4 w-4' 
                : density === 'spacious' 
                ? 'h-5 w-5' 
                : 'h-4 w-4'
            }`}
          />
        </td>
        {visibleColumns.name && (
          <td className={`pl-4 pr-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <div className="flex items-center">
              {/* Feature Icon */}
              <div className={`flex items-center justify-center mr-1 ${
                density === 'compact' 
                  ? 'w-8 h-8' 
                  : density === 'spacious' 
                  ? 'w-10 h-10' 
                  : 'w-8 h-8'
              }`}>
                <i className={`fas ${getFeatureIcon(feature)} ${
                  density === 'compact' 
                    ? 'text-lg' 
                    : density === 'spacious' 
                    ? 'text-xl' 
                    : 'text-lg'
                }`}></i>
              </div>

              {/* Feature Name */}
              <div className="flex-1 min-w-0">
                <span className={`text-gray-500 truncate font-bold ${
                  density === 'compact' 
                    ? 'text-sm' 
                    : density === 'spacious' 
                    ? 'text-base' 
                    : 'text-sm'
                }`}>
                  {feature.name}
                </span>
              </div>
            </div>
          </td>
        )}

        {visibleColumns.description && (
          <td className={`px-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`text-gray-500 truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            }`}>
              {feature.description || 'No description'}
            </span>
          </td>
        )}

        {visibleColumns.status && (
          <td className={`px-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`text-gray-500 truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            }`}>
              {feature.isIncluded ? 'Included' : 'Not Included'}
            </span>
          </td>
        )}

        {visibleColumns.price && (
          <td className={`px-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`text-gray-500 truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            }`}>
              ${feature.price || 0}
            </span>
          </td>
        )}

        {visibleColumns.discountRate && (
          <td className={`px-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`text-gray-500 truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            }`}>
              {feature.discountRate || 0}%
            </span>
          </td>
        )}

        {visibleColumns.totalDiscount && (
          <td className={`px-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`text-gray-500 truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            }`}>
              ${feature.totalDiscount || 0}
            </span>
          </td>
        )}

        {visibleColumns.actions && (
          <td className={`px-6 whitespace-nowrap text-right text-sm font-medium ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <div className="flex items-center justify-start space-x-2">
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onEdit(feature)
                }}
                className="text-blue-600 hover:text-blue-900 p-1 hover:scale-110"
                title="Edit feature"
              >
                <PencilIcon className={`${
                  density === 'compact' 
                    ? 'h-4 w-4' 
                    : density === 'spacious' 
                    ? 'h-5 w-5' 
                    : 'h-4 w-4'
                }`} />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete(feature)
                }}
                className="text-red-600 hover:text-red-900 p-1 hover:scale-110"
                title="Delete feature"
              >
                <TrashIcon className={`${
                  density === 'compact' 
                    ? 'h-4 w-4' 
                    : density === 'spacious' 
                    ? 'h-5 w-5' 
                    : 'h-4 w-4'
                }`} />
              </button>
            </div>
          </td>
        )}
      </tr>
    )
  })


  // Effects
  // Debounce search term to avoid excessive filtering
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchTerm])

  // Fetch features when option changes
  useEffect(() => {
    fetchFeatures()
  }, [option.id])

  // Handle click outside dropdowns to close them
  const handleClickOutside = useCallback((event: MouseEvent) => {
    const target = event.target as Element
    if (!target.closest('.dropdown-container')) {
      setShowFilters(false)
      setShowColumnSelector(false)
      setShowWindowList(false)
    }
  }, [])

  // Global click listener for dropdown management
  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [handleClickOutside])

  // Data Fetching
  // Fetch features for the current option with error handling
  const fetchFeatures = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/option-features?optionId=${option.id}&limit=100`)
      
      if (response.ok) {
        const data = await response.json()
        const featuresData = data.data || []
        setFeatures(featuresData)
        setFilteredFeatures(featuresData)
        
        if (featuresData.length > 0) {
          showSuccess('Features Loaded', `Loaded ${featuresData.length} feature${featuresData.length === 1 ? '' : 's'}`)
        } else {
          showInfo('No Features Found', 'This option has no features yet. Create the first one!')
        }
      } else {
        console.error('Failed to fetch features:', response.status, response.statusText)
        setFeatures([])
        setFilteredFeatures([])
      }
    } catch (error) {
      console.error('Error fetching features:', error)
      setFeatures([])
      setFilteredFeatures([])
    } finally {
      setLoading(false)
    }
  }, [option.id, showSuccess, showInfo])

  // CRUD Operations
  // Open form modal for creating new feature
  const handleCreateFeature = useCallback(() => {
    setIsFormOpen(true)
    setEditingFeature(null)
    showInfo('Create Feature', 'Fill out the form below to create a new feature.')
  }, [showInfo])

  // Open form modal for editing existing feature
  const handleEditFeature = useCallback((feature: OptionFeature) => {
    setEditingFeature(feature)
    setIsFormOpen(true)
    showInfo('Edit Feature', `Editing feature: "${feature.name}"`)
  }, [showInfo])

  // Check if feature can be safely deleted (no order dependencies)
  const checkFeatureDependencies = useCallback(async (feature: OptionFeature) => {
    try {
      const response = await fetch(`/api/admin/option-features/${feature.id}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch feature details')
      }
      
      const data = await response.json()
      const freshFeature = data.data
      const orderDetailsCount = freshFeature._count?.orderDetails || 0
      
      const dependencies: string[] = []
      let canDelete = true
      let reason = ''
      
      if (orderDetailsCount > 0) {
        dependencies.push(`${orderDetailsCount} order${orderDetailsCount === 1 ? '' : 's'}`)
        canDelete = false
        reason = 'Feature is associated with existing orders'
      } else {
        reason = 'No dependencies found - safe to delete'
      }
      
      setVerificationData({
        canDelete,
        reason,
        orderDetails: orderDetailsCount,
        dependencies
      })
    } catch (error) {
      console.error('Error checking feature dependencies:', error)
      setVerificationData({
        canDelete: false,
        reason: 'Error checking dependencies',
        dependencies: ['Unable to verify dependencies']
      })
    }
  }, [])

  // Initiate delete process with dependency verification
  const handleDeleteFeature = useCallback(async (feature: OptionFeature) => {
    setFeatureToDelete(feature)
    showLoading('Verifying Feature', `Checking if "${feature.name}" can be deleted...`)
    await checkFeatureDependencies(feature)
    setIsDeleteModalOpen(true)
  }, [checkFeatureDependencies, showLoading])

  // Execute bulk delete after confirmation
  const executeBulkDelete = useCallback(async () => {
    if (!bulkFeaturesToDelete.length) return

    try {
      // If not all features can be deleted, only delete the safe ones
      if (!verificationData?.canDelete) {
        showLoading('Deleting Safe Features', 'Deleting only features without dependencies...')
        
        // Re-verify each feature to get fresh data
        const verificationPromises = bulkFeaturesToDelete.map(async (feature) => {
          try {
            const response = await fetch(`/api/admin/option-features/${feature.id}`)
            if (response.ok) {
              const featureData = await response.json()
              const freshFeature = featureData.data
              const orderDetailsCount = freshFeature._count?.orderDetails || 0
              return {
                feature,
                canDelete: orderDetailsCount === 0
              }
            }
            return { feature, canDelete: false }
          } catch (error) {
            return { feature, canDelete: false }
          }
        })
        
        const verificationResults = await Promise.all(verificationPromises)
        const safeToDelete = verificationResults.filter(result => result.canDelete)
        const unsafeToDelete = verificationResults.filter(result => !result.canDelete)
        
        if (safeToDelete.length === 0) {
          showWarning('No Safe Features', 'None of the selected features can be deleted due to dependencies.')
          setIsDeleteModalOpen(false)
          setBulkFeaturesToDelete([])
          setIsBulkDelete(false)
          setVerificationData(null)
          return
        }
        
        // Delete only safe features
        const deletePromises = safeToDelete.map(result => 
          fetch(`/api/admin/option-features/${result.feature.id}`, {
            method: 'DELETE',
          })
        )
        
        await Promise.all(deletePromises)
        
        let message = `Successfully deleted ${safeToDelete.length} features.`
        if (unsafeToDelete.length > 0) {
          message += ` ${unsafeToDelete.length} features were skipped due to dependencies.`
        }
        
        showSuccess('Features Deleted', message)
      } else {
        // All features are safe to delete
        showLoading('Deleting Features', `Deleting ${bulkFeaturesToDelete.length} features...`)
        
        const promises = bulkFeaturesToDelete.map(feature => 
          fetch(`/api/admin/option-features/${feature.id}`, { method: 'DELETE' })
        )
        
        await Promise.all(promises)
        showSuccess('Features Deleted', `Successfully deleted ${bulkFeaturesToDelete.length} features.`)
      }
      
      setFeatures(prev => prev.filter(feature => !bulkFeaturesToDelete.map(f => f.id).includes(feature.id)))
      setFilteredFeatures(prev => prev.filter(feature => !bulkFeaturesToDelete.map(f => f.id).includes(feature.id)))
      setSelectedFeatures([])
      setShowBulkActions(false)
    } catch (error) {
      console.error('Error deleting features:', error)
      showError('Delete Error', 'Failed to delete selected features.')
    } finally {
      setIsDeleteModalOpen(false)
      setBulkFeaturesToDelete([])
      setIsBulkDelete(false)
      setVerificationData(null)
    }
  }, [bulkFeaturesToDelete, verificationData, showLoading, showSuccess, showWarning, showError])

  // Confirm and execute feature deletion
  const confirmDeleteFeature = useCallback(async () => {
    if (isBulkDelete) {
      await executeBulkDelete()
      return
    }

    if (!featureToDelete) return
    
    try {
      showLoading('Deleting Feature', 'Removing feature from the system...')
      
      const response = await fetch(`/api/admin/option-features/${featureToDelete.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setFeatures(prev => prev.filter(feature => feature.id !== featureToDelete.id))
        setFilteredFeatures(prev => prev.filter(feature => feature.id !== featureToDelete.id))
        showSuccess('Feature Deleted', `"${featureToDelete.name}" has been successfully removed.`)
      } else {
        const errorData = await response.json()
        showError('Delete Failed', errorData.message || 'Failed to delete feature')
      }
    } catch (error) {
      console.error('Error deleting feature:', error)
      showError('Delete Failed', 'Failed to delete feature')
    } finally {
      setIsDeleteModalOpen(false)
      setFeatureToDelete(null)
    }
  }, [isBulkDelete, featureToDelete, executeBulkDelete, showLoading, showSuccess, showError])

  // Handle form submission for both create and update operations
  const handleSubmitForm = useCallback(async (formData: any) => {
    const action = editingFeature ? 'Updating' : 'Creating'
    showLoading(`${action} Feature`, `${action.toLowerCase()} feature...`)
    
    try {
      const url = editingFeature 
        ? `/api/admin/option-features/${editingFeature.id}`
        : '/api/admin/option-features'
      
      const method = editingFeature ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description,
          optionid: option.id,
          price: formData.price,
          discountrate: formData.discountRate,
          totaldiscount: formData.totalDiscount,
          isincluded: formData.isIncluded
        })
      })

      if (response.ok) {
        const data = await response.json()
        
        if (editingFeature) {
          const updatedFeature = { ...editingFeature, ...formData }
          setFeatures(prev => prev.map(feature => feature.id === editingFeature.id ? updatedFeature : feature))
          setFilteredFeatures(prev => prev.map(feature => feature.id === editingFeature.id ? updatedFeature : feature))
          showSuccess('Feature Updated', `"${editingFeature.name}" has been successfully updated.`)
        } else {
          const newFeature = data.data
          setFeatures(prev => [...prev, newFeature])
          setFilteredFeatures(prev => [...prev, newFeature])
          showSuccess('Feature Created', `"${formData.name}" has been successfully created.`)
        }
        
        setIsFormOpen(false)
        setEditingFeature(null)
      } else {
        const errorData = await response.json()
        showError('Save Failed', errorData.message || 'Failed to save feature')
      }
    } catch (error) {
      console.error('Error saving feature:', error)
      showError('Save Failed', 'Failed to save feature')
    }
  }, [editingFeature, option.id, showLoading, showSuccess, showError])

  // Toggle feature inclusion status with API update
  const toggleFeatureIncluded = useCallback(async (featureId: string) => {
    try {
      const currentFeature = features.find(f => f.id === featureId)
      if (!currentFeature) return
      
      const response = await fetch(`/api/admin/option-features/${featureId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: currentFeature.name,
          description: currentFeature.description,
          optionid: currentFeature.optionId,
          price: 0,
          discountrate: 0,
          totaldiscount: 0,
          isincluded: !currentFeature.isIncluded
        })
      })

      if (response.ok) {
        const updateFeatures = (prev: OptionFeature[]) => 
          prev.map(feature => 
            feature.id === featureId 
              ? { ...feature, isIncluded: !feature.isIncluded }
              : feature
          )
        
        setFeatures(updateFeatures)
        setFilteredFeatures(updateFeatures)
        showSuccess('Feature Status Updated', currentFeature.isIncluded ? 'Feature marked as not included' : 'Feature marked as included')
      } else {
        const errorData = await response.json()
        showError('Update Failed', errorData.message || 'Failed to toggle feature status')
      }
    } catch (error) {
      console.error('Error toggling feature:', error)
      showError('Update Failed', 'Failed to toggle feature status')
    }
  }, [features, showSuccess, showError])

  // Computed Values
  // Apply search, filters, and sorting to features list
  const filteredAndSortedFeatures = useMemo(() => {
    let filtered = features

    // Search by name or description
    if (debouncedSearchTerm) {
      filtered = filtered.filter(feature =>
        feature.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
        feature.description?.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
      )
    }

    // Filter by inclusion status
    if (currentFilters.status) {
      filtered = filtered.filter(feature => {
        if (currentFilters.status === 'included') return feature.isIncluded
        if (currentFilters.status === 'not-included') return !feature.isIncluded
        return true
      })
    }

    // Filter by price (free vs paid)
    if (currentFilters.price) {
      filtered = filtered.filter(feature => {
        const price = feature.price || 0
        if (currentFilters.price === 'free') return price === 0
        if (currentFilters.price === 'paid') return price > 0
        return true
      })
    }

    // Apply sorting with type-specific comparison logic
    if (sortField) {
      filtered.sort((a, b) => {
        let aValue: any = a[sortField as keyof OptionFeature]
        let bValue: any = b[sortField as keyof OptionFeature]

        if (sortField === 'name' || sortField === 'description') {
          aValue = (aValue || '').toLowerCase()
          bValue = (bValue || '').toLowerCase()
        } else if (sortField === 'isIncluded') {
          aValue = aValue ? 1 : 0
          bValue = bValue ? 1 : 0
        } else if (sortField === 'price' || sortField === 'discountRate' || sortField === 'totalDiscount') {
          aValue = aValue || 0
          bValue = bValue || 0
        } else if (sortField === 'createdAt' || sortField === 'updatedAt') {
          aValue = new Date(aValue).getTime()
          bValue = new Date(bValue).getTime()
        }

        if (sortDirection === 'asc') {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
        } else {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
        }
      })
    }

    return filtered
  }, [features, debouncedSearchTerm, currentFilters, sortField, sortDirection])

  // Update filtered features and reset pagination when filters change
  useEffect(() => {
    setFilteredFeatures(filteredAndSortedFeatures)
    setCurrentPage(1)
  }, [filteredAndSortedFeatures])
  
  // Pagination data calculation
  const paginationData = useMemo(() => {
    const totalPages = Math.ceil(filteredFeatures.length / itemsPerPage)
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    const paginatedFeatures = filteredFeatures.slice(startIndex, endIndex)
    
    return { totalPages, startIndex, endIndex, paginatedFeatures }
  }, [filteredFeatures, itemsPerPage, currentPage])
  
  // Pagination handlers
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page)
  }, [])
  
  const handleItemsPerPageChange = useCallback((items: number) => {
    setItemsPerPage(items)
    setCurrentPage(1)
  }, [])
  
  // Calculate space needed for dropdowns to prevent layout shift
  const dropdownSpaceNeeded = useMemo(() => {
    let spaceNeeded = 0
    
    if (showColumnSelector) spaceNeeded = Math.max(spaceNeeded, 200)
    if (showWindowList) spaceNeeded = Math.max(spaceNeeded, 120)
    if (showFilters) spaceNeeded = Math.max(spaceNeeded, 300)
    
    return spaceNeeded
  }, [showColumnSelector, showWindowList, showFilters])

  // Rendering Functions
  // Render individual feature card with hover effects and action menu
  const renderFeatureCard = useCallback((feature: OptionFeature, isLargeCard: boolean = false) => {
    // Dynamic padding based on density setting
    const getDensityPadding = () => {
      if (isLargeCard) return 'p-6'
      switch (density) {
        case 'compact': return 'p-2'
        case 'comfortable': return 'p-4'
        case 'spacious': return 'p-6'
        default: return 'p-4'
      }
    }

    return (
      <div
        key={feature.id}
        className={`group relative bg-white border border-gray-200 rounded-lg cursor-pointer overflow-hidden hover:cursor-pointer shadow-sm hover:shadow-md hover:bg-gray-50/50 hover:border-gray-300 ${
          selectedFeatures.includes(feature.id) 
            ? 'bg-purple-50 border-l-4 border-purple-500' 
            : ''
        } ${getDensityPadding()}`}
        data-section="features"
        // Show/hide action menu on hover with smooth transitions
        onMouseEnter={(e) => {
          // Only show action menu on hover for desktop (lg and up)
          if (window.innerWidth >= 1024) {
            const actionMenu = e.currentTarget.querySelector('.action-menu') as HTMLElement;
            if (actionMenu) {
              actionMenu.style.opacity = '1';
              actionMenu.style.transform = 'translateX(0)';
              actionMenu.style.pointerEvents = 'auto';
            }
          }
        }}
        onMouseLeave={(e) => {
          // Only hide action menu on mouse leave for desktop (lg and up)
          if (window.innerWidth >= 1024) {
            const actionMenu = e.currentTarget.querySelector('.action-menu') as HTMLElement;
            if (actionMenu) {
              actionMenu.style.opacity = '0';
              actionMenu.style.transform = 'translateX(100%)';
              actionMenu.style.pointerEvents = 'none';
            }
          }
        }}
      >
        {/* Header Section */}
        <div className={`flex items-start justify-between ${
          density === 'compact' ? 'mb-2' : density === 'spacious' ? 'mb-6' : 'mb-4'
        }`}>
          <div className={`flex items-start flex-1 min-w-0 ${
            density === 'compact' ? 'space-x-2' : density === 'spacious' ? 'space-x-4' : 'space-x-3'
          }`}>
            {/* Checkbox */}
            <div className="flex-shrink-0 pt-1" onClick={(e) => e.stopPropagation()}>
              <input
                type="checkbox"
                checked={selectedFeatures.includes(feature.id)}
                onChange={(e) => {
                  e.stopPropagation()
                  handleSelectFeature(feature.id)
                }}
                onClick={(e) => e.stopPropagation()}
                className={`h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded ${
                  density === 'compact' 
                    ? 'h-3 w-3' 
                    : density === 'spacious' 
                    ? 'h-5 w-5' 
                    : 'h-4 w-4'
                }`}
              />
            </div>
            
            {/* Icon Container */}
            <div className={`flex-shrink-0 bg-purple-50 rounded-lg border border-purple-100 ${
              density === 'compact' 
                ? 'p-2' 
                : density === 'spacious' 
                ? 'p-4' 
                : 'p-3'
            }`}>
              <i className={`fas ${getFeatureIcon(feature)} text-purple-600 ${
                density === 'compact' 
                  ? 'text-lg' 
                  : density === 'spacious' 
                  ? 'text-2xl' 
                  : 'text-xl'
              }`}></i>
            </div>
            
            {/* Title and Description */}
            <div className="flex-1 min-w-0">
              <h3 className={`font-semibold text-gray-900 truncate ${
                density === 'compact' 
                  ? 'text-base mb-0.5' 
                  : density === 'spacious' 
                  ? 'text-xl mb-2' 
                  : 'text-lg mb-1'
              }`}>
                {feature.name}
              </h3>
              {feature.description && (
                <p className={`text-gray-600 line-clamp-2 leading-relaxed ${
                  density === 'compact' 
                    ? 'text-xs' 
                    : density === 'spacious' 
                    ? 'text-base' 
                    : 'text-sm'
                }`}>
                  {feature.description}
                </p>
              )}
            </div>
          </div>

          {/* Status Badge */}
          <span className={`inline-flex items-center rounded-full font-semibold ${
            density === 'compact' 
              ? 'px-2 py-1 text-xs' 
              : density === 'spacious' 
              ? 'px-4 py-2 text-sm' 
              : 'px-3 py-1.5 text-xs'
          } ${
            feature.isIncluded
              ? 'bg-green-100 text-green-800 border border-green-200'
              : 'bg-red-100 text-red-800 border border-red-200'
          }`}>
            <div className={`rounded-full mr-2 ${
              density === 'compact' 
                ? 'w-1.5 h-1.5' 
                : density === 'spacious' 
                ? 'w-2.5 h-2.5' 
                : 'w-2 h-2'
            } ${
              feature.isIncluded ? 'bg-green-500' : 'bg-red-500'
            }`}></div>
            {feature.isIncluded ? 'Included' : 'Not Included'}
          </span>
        </div>

        {/* Pricing Information */}
        <div className={`${
          density === 'compact' ? 'space-y-2 mb-2' : density === 'spacious' ? 'space-y-4 mb-6' : 'space-y-3 mb-4'
        }`}>
          {/* Pricing Grid */}
          <div className="grid grid-cols-2 gap-3">
            {/* Base Price */}
            <div className={`bg-gray-50 rounded-lg ${
              density === 'compact' 
                ? 'p-2' 
                : density === 'spacious' 
                ? 'p-4' 
                : 'p-3'
            }`}>
              <div className="flex items-center space-x-2 mb-1">
                <div className={`bg-white rounded-lg flex items-center justify-center shadow-sm ${
                  density === 'compact' 
                    ? 'w-5 h-5' 
                    : density === 'spacious' 
                    ? 'w-8 h-8' 
                    : 'w-6 h-6'
                }`}>
                  <i className={`fas fa-dollar-sign text-green-600 ${
                    density === 'compact' 
                      ? 'text-xs' 
                      : density === 'spacious' 
                      ? 'text-sm' 
                      : 'text-xs'
                  }`}></i>
                </div>
                <div className={`font-medium text-gray-500 uppercase tracking-wide ${
                  density === 'compact' 
                    ? 'text-xs' 
                    : density === 'spacious' 
                    ? 'text-sm' 
                    : 'text-xs'
                }`}>
                  Base Price
                </div>
              </div>
              <div className={`font-bold text-gray-900 ${
                density === 'compact' 
                  ? 'text-sm' 
                  : density === 'spacious' 
                  ? 'text-lg' 
                  : 'text-base'
              }`}>
                ${feature.price || 0}
              </div>
            </div>

            {/* Discount Rate */}
            <div className={`bg-gray-50 rounded-lg ${
              density === 'compact' 
                ? 'p-2' 
                : density === 'spacious' 
                ? 'p-4' 
                : 'p-3'
            }`}>
              <div className="flex items-center space-x-2 mb-1">
                <div className={`bg-white rounded-lg flex items-center justify-center shadow-sm ${
                  density === 'compact' 
                    ? 'w-5 h-5' 
                    : density === 'spacious' 
                    ? 'w-8 h-8' 
                    : 'w-6 h-6'
                }`}>
                  <i className={`fas fa-percentage text-blue-600 ${
                    density === 'compact' 
                      ? 'text-xs' 
                      : density === 'spacious' 
                      ? 'text-sm' 
                      : 'text-xs'
                  }`}></i>
                </div>
                <div className={`font-medium text-gray-500 uppercase tracking-wide ${
                  density === 'compact' 
                    ? 'text-xs' 
                    : density === 'spacious' 
                    ? 'text-sm' 
                    : 'text-xs'
                }`}>
                  Discount Rate
                </div>
              </div>
              <div className={`font-bold ${
                feature.discountRate && feature.discountRate > 0 
                  ? 'text-blue-600' 
                  : 'text-gray-400'
              } ${
                density === 'compact' 
                  ? 'text-sm' 
                  : density === 'spacious' 
                  ? 'text-lg' 
                  : 'text-base'
              }`}>
                {feature.discountRate || 0}%
              </div>
            </div>
          </div>

          {/* Total Discount */}
          <div className={`rounded-lg ${
            (feature.price || 0) > 0 && (feature.discountRate || 0) > 0
              ? 'bg-red-50 border border-red-100' 
              : 'bg-gray-50'
          } ${
            density === 'compact' 
              ? 'p-2' 
              : density === 'spacious' 
              ? 'p-4' 
              : 'p-3'
          }`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className={`bg-white rounded-lg flex items-center justify-center shadow-sm ${
                  density === 'compact' 
                    ? 'w-5 h-5' 
                    : density === 'spacious' 
                    ? 'w-8 h-8' 
                    : 'w-6 h-6'
                }`}>
                  <i className={`fas fa-tag ${
                    (feature.price || 0) > 0 && (feature.discountRate || 0) > 0
                      ? 'text-red-600' 
                      : 'text-gray-400'
                  } ${
                    density === 'compact' 
                      ? 'text-xs' 
                      : density === 'spacious' 
                      ? 'text-sm' 
                      : 'text-xs'
                  }`}></i>
                </div>
                <div>
                  <div className={`font-medium text-gray-500 uppercase tracking-wide ${
                    density === 'compact' 
                      ? 'text-xs' 
                      : density === 'spacious' 
                      ? 'text-sm' 
                      : 'text-xs'
                  }`}>
                    Total Discount
                  </div>
                  <div className={`font-bold ${
                    (feature.price || 0) > 0 && (feature.discountRate || 0) > 0
                      ? 'text-red-600' 
                      : 'text-gray-400'
                  } ${
                    density === 'compact' 
                      ? 'text-sm' 
                      : density === 'spacious' 
                      ? 'text-lg' 
                      : 'text-base'
                  }`}>
                    {(() => {
                      const basePrice = feature.price || 0;
                      const discountRate = feature.discountRate || 0;
                      const calculatedDiscount = basePrice * (discountRate / 100);
                      return calculatedDiscount > 0 ? `-$${calculatedDiscount.toFixed(2)}` : '$0';
                    })()}
                  </div>
                </div>
              </div>
              {/* Calculate final price after discount */}
              <div className="text-right">
                <div className={`font-medium text-gray-500 uppercase tracking-wide ${
                  density === 'compact' 
                    ? 'text-xs' 
                    : density === 'spacious' 
                    ? 'text-sm' 
                    : 'text-xs'
                }`}>
                  Final Price
                </div>
                <div className={`font-bold text-green-600 ${
                  density === 'compact' 
                    ? 'text-sm' 
                    : density === 'spacious' 
                    ? 'text-lg' 
                    : 'text-base'
                }`}>
                  {(() => {
                    const basePrice = feature.price || 0;
                    const discountRate = feature.discountRate || 0;
                    const calculatedDiscount = basePrice * (discountRate / 100);
                    const finalPrice = basePrice - calculatedDiscount;
                    return `$${finalPrice.toFixed(2)}`;
                  })()}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer Information */}
        <div className={`border-t border-gray-100 ${
          density === 'compact' ? 'pt-2' : density === 'spacious' ? 'pt-4' : 'pt-3'
        }`}>
          <div className={`flex items-center justify-between text-gray-500 ${
            density === 'compact' 
              ? 'text-xs' 
              : density === 'spacious' 
              ? 'text-sm' 
              : 'text-xs'
          }`}>
            <div className={`flex items-center ${
              density === 'compact' ? 'space-x-2' : density === 'spacious' ? 'space-x-6' : 'space-x-4'
            }`}>
              <span className={`flex items-center ${
                density === 'compact' ? 'space-x-0.5' : density === 'spacious' ? 'space-x-2' : 'space-x-1'
              }`}>
                <i className={`fas fa-calendar-plus text-gray-400 ${
                  density === 'compact' 
                    ? 'text-xs' 
                    : density === 'spacious' 
                    ? 'text-sm' 
                    : 'text-xs'
                }`}></i>
                <span>{new Date(feature.createdAt).toLocaleDateString()}</span>
              </span>
              <span className={`flex items-center ${
                density === 'compact' ? 'space-x-0.5' : density === 'spacious' ? 'space-x-2' : 'space-x-1'
              }`}>
                <i className={`fas fa-calendar-edit text-gray-400 ${
                  density === 'compact' 
                    ? 'text-xs' 
                    : density === 'spacious' 
                    ? 'text-sm' 
                    : 'text-xs'
                }`}></i>
                <span>{new Date(feature.updatedAt).toLocaleDateString()}</span>
              </span>
            </div>
            <div className={`flex items-center ${
              density === 'compact' ? 'space-x-0.5' : density === 'spacious' ? 'space-x-2' : 'space-x-1'
            }`}>
              <i className={`fas fa-hashtag text-gray-400 ${
                density === 'compact' 
                  ? 'text-xs' 
                  : density === 'spacious' 
                  ? 'text-sm' 
                  : 'text-xs'
              }`}></i>
              <span className={`font-mono ${
                density === 'compact' 
                  ? 'text-xs' 
                  : density === 'spacious' 
                  ? 'text-sm' 
                  : 'text-xs'
              }`}>{feature.id.slice(-6)}</span>
              
              {/* Mobile Action Button - Always visible on mobile and small screens */}
              <div className="lg:hidden ml-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    const actionMenu = e.currentTarget.closest('.group')?.querySelector('.action-menu') as HTMLElement;
                    if (actionMenu) {
                      const isVisible = actionMenu.style.opacity === '1';
                      actionMenu.style.opacity = isVisible ? '0' : '1';
                      actionMenu.style.transform = isVisible ? 'translateX(100%)' : 'translateX(0)';
                      actionMenu.style.pointerEvents = isVisible ? 'none' : 'auto';
                    }
                  }}
                  className="p-2 text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                  title="Show Actions"
                >
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Actions Sidebar - Professional Overlay with smooth animations */}
        <div className={`action-menu absolute bg-white rounded-lg border border-gray-200 flex flex-col items-center justify-center transition-all duration-200 ${
          density === 'compact' 
            ? 'top-2 right-2 bottom-2 w-10 space-y-2' 
            : density === 'spacious' 
            ? 'top-4 right-4 bottom-4 w-14 space-y-6' 
            : 'top-3 right-3 bottom-3 w-12 space-y-4'
        }`} style={{
          opacity: '0',
          transform: 'translateX(100%)',
          pointerEvents: 'none'
        }}>
          {/* Edit Button */}
          <button
            onClick={(e) => {
              e.stopPropagation()
              handleEditFeature(feature)
            }}
            className={`flex items-center justify-center bg-blue-500 text-white rounded-lg border border-blue-600 hover:scale-110 shadow-sm pointer-events-auto ${
              density === 'compact' 
                ? 'w-7 h-7' 
                : density === 'spacious' 
                ? 'w-10 h-10' 
                : 'w-8 h-8'
            }`}
            title="Edit Feature"
          >
            <PencilIcon className={`${
              density === 'compact' 
                ? 'h-3 w-3' 
                : density === 'spacious' 
                ? 'h-5 w-5' 
                : 'h-4 w-4'
            }`} />
          </button>
          
          {/* Toggle Include Button */}
          <button
            onClick={(e) => {
              e.stopPropagation()
              toggleFeatureIncluded(feature.id)
            }}
            className={`flex items-center justify-center rounded-lg border hover:scale-110 shadow-sm pointer-events-auto ${
              density === 'compact' 
                ? 'w-7 h-7' 
                : density === 'spacious' 
                ? 'w-10 h-10' 
                : 'w-8 h-8'
            } ${
              feature.isIncluded
                ? 'bg-emerald-500 border-emerald-600 text-white'
                : 'bg-gray-400 border-gray-500 text-white'
            }`}
            title={feature.isIncluded ? 'Mark as not included' : 'Mark as included'}
          >
            {feature.isIncluded ? (
              <XMarkIcon className={`${
                density === 'compact' 
                  ? 'h-3 w-3' 
                  : density === 'spacious' 
                  ? 'h-5 w-5' 
                  : 'h-4 w-4'
              }`} />
            ) : (
              <CheckIcon className={`${
                density === 'compact' 
                  ? 'h-3 w-3' 
                  : density === 'spacious' 
                  ? 'h-5 w-5' 
                  : 'h-4 w-4'
              }`} />
            )}
          </button>
          
          
          {/* Delete Button */}
          <button
            onClick={(e) => {
              e.stopPropagation()
              handleDeleteFeature(feature)
            }}
            className={`flex items-center justify-center bg-red-500 text-white rounded-lg border border-red-600 hover:scale-110 shadow-sm pointer-events-auto ${
              density === 'compact' 
                ? 'w-7 h-7' 
                : density === 'spacious' 
                ? 'w-10 h-10' 
                : 'w-8 h-8'
            }`}
            title="Delete Feature"
          >
            <TrashIcon className={`${
              density === 'compact' 
                ? 'h-3 w-3' 
                : density === 'spacious' 
                ? 'h-5 w-5' 
                : 'h-4 w-4'
            }`} />
          </button>
        </div>
      </div>
    )
  }, [density, selectedFeatures, getFeatureIcon, handleSelectFeature, handleEditFeature, handleDeleteFeature, toggleFeatureIncluded])

  // Early return for loading state
  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Main component render with dynamic bottom padding for dropdowns
  return (
    <div className="space-y-3" data-section="features" style={{ paddingBottom: dropdownSpaceNeeded > 0 ? `${dropdownSpaceNeeded}px` : '0' }}>
      {/* Mobile Layout */}
      <div className="flex flex-col space-y-3 lg:hidden">
        {/* Search Bar - Full Width on Mobile */}
        <div className="w-full">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search features..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm"
            />
          </div>
        </div>

        {/* Mobile Controls - Single Row */}
        <div className="flex items-center gap-1">
          {/* View Mode Toggle - Stretched */}
          <div className="flex items-center bg-gray-100 rounded-lg p-0.5 flex-1">
            <button
              onClick={() => setViewMode('list')}
              className={`flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 ${
                viewMode === 'list'
                  ? 'bg-white text-purple-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              title="List view"
            >
              <ListBulletIcon className="h-3 w-3" />
              <span className="text-xs font-medium hidden xs:inline">List</span>
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 ${
                viewMode === 'grid'
                  ? 'bg-white text-purple-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              title="Grid view"
            >
              <Squares2X2Icon className="h-3 w-3" />
              <span className="text-xs font-medium hidden xs:inline">Grid</span>
            </button>
          </div>

          {/* Grid Columns Control (for grid view) */}
          {viewMode === 'grid' && (
            <div className="flex items-center bg-gray-100 rounded-lg p-0.5 flex-1">
              <span className="text-xs font-medium text-gray-700 px-1">Col:</span>
              <div className="flex items-center gap-0.5 flex-1">
                {[1, 2, 3, 4].map((num) => (
                  <button
                    key={num}
                    onClick={() => setGridColumns(num)}
                    className={`flex-1 px-1.5 py-1 rounded text-xs font-medium ${
                      gridColumns === num
                        ? 'bg-white text-green-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                    title={`${num} column${num > 1 ? 's' : ''}`}
                  >
                    {num}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Table Columns Control (for list view) */}
          {viewMode === 'list' && (
            <div className="relative flex-1">
              <button
                onClick={() => setShowColumnSelector(!showColumnSelector)}
                className="w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                title="Columns"
              >
                <ViewColumnsIcon className="h-3 w-3 mr-0.5" />
                <span className="hidden xs:inline">Col</span>
                <ChevronDownIcon className="h-3 w-3 ml-0.5" />
              </button>
            </div>
          )}

          {/* Filters Button - Stretched */}
          <div className="relative flex-1">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium rounded-lg border ${
                showFilters || Object.keys(currentFilters).some(key => currentFilters[key as keyof Filters])
                  ? 'bg-purple-50 text-purple-700 border-purple-300'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              title="Filters"
            >
              <FunnelIcon className="h-3 w-3 mr-0.5" />
              <span className="hidden xs:inline">Filter</span>
              {Object.keys(currentFilters).some(key => currentFilters[key as keyof Filters]) && (
                <span className="ml-0.5 inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  {Object.values(currentFilters).filter(Boolean).length}
                </span>
              )}
            </button>
          </div>

          {/* Density Control - Stretched */}
          <div className="relative flex-1">
            <button
              onClick={() => setShowWindowList(!showWindowList)}
              className="w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
              title="Density"
            >
              <AdjustmentsHorizontalIcon className="h-3 w-3 mr-0.5" />
              <span className="hidden xs:inline">{density.charAt(0).toUpperCase() + density.slice(1)}</span>
              <ChevronDownIcon className="h-3 w-3 ml-0.5" />
            </button>
          </div>

          {/* Create Button - Stretched */}
          <button
            onClick={() => setIsFormOpen(true)}
            className="flex-1 inline-flex items-center justify-center px-3 py-1.5 bg-purple-600 text-white text-xs font-medium rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            <PlusIcon className="h-3 w-3 mr-0.5" />
            <span className="hidden xs:inline">Add</span>
          </button>
        </div>
      </div>

      {/* Desktop Layout - Horizontal */}
      <div className="hidden lg:flex items-center justify-between gap-4 mb-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm">
        {/* Search Bar and Filters */}
        <div className="flex items-center gap-3 flex-1 max-w-md">
          <div className="relative flex-1">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search features by name or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm"
            />
          </div>

          {/* Filters Button with active filter count */}
          <div className="relative dropdown-container">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500"
              title="Filter features"
            >
              <FunnelIcon className="h-4 w-4 mr-2" />
              Filters
              {Object.keys(currentFilters).some(key => currentFilters[key as keyof Filters]) && (
                <span className="ml-2 inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-purple-600 rounded-full">
                  {Object.values(currentFilters).filter(Boolean).length}
                </span>
              )}
              <ChevronDownIcon className="h-4 w-4 ml-2" />
            </button>
            {showFilters && (
              <div className="hidden lg:block absolute top-full right-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                <div className="p-4">
                  <h3 className="text-sm font-medium text-gray-900 mb-3">Filter Features</h3>
                  
                  {/* Status Filter */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select
                      value={currentFilters.status || ''}
                      onChange={(e) => setCurrentFilters(prev => ({ ...prev, status: e.target.value || undefined }))}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    >
                      <option value="">All Statuses</option>
                      <option value="included">Included</option>
                      <option value="not-included">Not Included</option>
                    </select>
                  </div>

                  {/* Price Filter */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Price</label>
                    <select
                      value={currentFilters.price || ''}
                      onChange={(e) => setCurrentFilters(prev => ({ ...prev, price: e.target.value || undefined }))}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    >
                      <option value="">All Prices</option>
                      <option value="free">Free</option>
                      <option value="paid">Paid</option>
                    </select>
                  </div>

                  <div className="flex justify-end mt-4 pt-4 border-t border-gray-200">
                    <button
                      onClick={() => {
                        setCurrentFilters({})
                        setShowFilters(false)
                      }}
                      className="px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg"
                    >
                      Clear All
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* View Options and Controls */}
        <div className="flex items-center gap-3">
          {/* View Mode Toggle - List vs Grid */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">View:</span>
            <div className="flex items-center bg-gray-100 rounded-lg p-1 gap-1">
              <button
                onClick={() => setViewMode('list')}
                className={`px-3 py-2 rounded-md flex items-center gap-2 ${
                  viewMode === 'list'
                    ? 'bg-white text-purple-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="List view"
              >
                <ListBulletIcon className="h-5 w-5" />
                <span className="text-sm font-medium">List</span>
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`px-3 py-2 rounded-md flex items-center gap-2 ${
                  viewMode === 'grid'
                    ? 'bg-white text-purple-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Grid view"
              >
                <Squares2X2Icon className="h-5 w-5" />
                <span className="text-sm font-medium">Grid</span>
              </button>
            </div>
          </div>

          {/* Grid Columns Control (for grid view only) */}
          {viewMode === 'grid' && (
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700">Columns:</span>
              <div className="flex items-center bg-gray-100 rounded-lg p-1 gap-1">
                <button
                  onClick={() => setGridColumns(1)}
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    gridColumns === 1
                      ? 'bg-white text-purple-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="1 column"
                >
                  1
                </button>
                <button
                  onClick={() => setGridColumns(2)}
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    gridColumns === 2
                      ? 'bg-white text-purple-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="2 columns"
                >
                  2
                </button>
                <button
                  onClick={() => setGridColumns(3)}
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    gridColumns === 3
                      ? 'bg-white text-purple-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="3 columns"
                >
                  3
                </button>
                <button
                  onClick={() => setGridColumns(4)}
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    gridColumns === 4
                      ? 'bg-white text-purple-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="4 columns"
                >
                  4
                </button>
              </div>
            </div>
          )}

          {/* Column Selector (List View Only) */}
          {viewMode === 'list' && (
            <div className="relative dropdown-container">
              <button
                onClick={() => setShowColumnSelector(!showColumnSelector)}
                className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500"
                title="Select columns to display"
              >
                <ViewColumnsIcon className="h-4 w-4 mr-2" />
                Columns
                <ChevronDownIcon className="h-4 w-4 ml-2" />
              </button>
              {showColumnSelector && (
                <div className="hidden lg:block absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                  <div className="p-2">
                    {Object.entries(visibleColumns).map(([key, visible]) => (
                      <label key={key} className="flex items-center space-x-2 py-1">
                        <input
                          type="checkbox"
                          checked={visible}
                          onChange={(e) => setVisibleColumns(prev => ({ ...prev, [key]: e.target.checked }))}
                          className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                        />
                        <span className="text-sm text-gray-700 capitalize">{key}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Density Control - Adjust spacing and padding */}
          <div className="relative dropdown-container">
            <button
              onClick={() => setShowWindowList(!showWindowList)}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500"
              title="Adjust table density"
            >
              <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
              {density.charAt(0).toUpperCase() + density.slice(1)}
              <ChevronDownIcon className="h-4 w-4 ml-2" />
            </button>
            {showWindowList && (
              <div className="hidden lg:block absolute top-full left-0 mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                <div className="p-1">
                  {(['compact', 'comfortable', 'spacious'] as const).map((option) => (
                    <button
                      key={option}
                      onClick={() => {
                        handleDensityChange(option)
                        setShowWindowList(false)
                      }}
                      className={`w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100 ${
                        density === option ? 'bg-purple-100 text-purple-700' : 'text-gray-700'
                      }`}
                    >
                      {option.charAt(0).toUpperCase() + option.slice(1)}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>


          {/* Create Button - Primary action */}
          <button
            onClick={handleCreateFeature}
            className="inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Feature
          </button>
        </div>
      </div>

      {/* Mobile Dropdowns */}
      {/* Filters Dropdown - Mobile */}
      {showFilters && (
        <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-900">Filter Features</h3>
            <button
              onClick={() => setShowFilters(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <select
                value={currentFilters.status || ''}
                onChange={(e) => setCurrentFilters(prev => ({ ...prev, status: e.target.value || undefined }))}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
              <select
                value={currentFilters.type || ''}
                onChange={(e) => setCurrentFilters(prev => ({ ...prev, type: e.target.value || undefined }))}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Types</option>
                <option value="standard">Standard</option>
                <option value="premium">Premium</option>
                <option value="enterprise">Enterprise</option>
              </select>
            </div>
          </div>
          
          <div className="flex justify-end mt-4 pt-4 border-t border-gray-200">
            <button
              onClick={() => {
                setCurrentFilters({})
                setShowFilters(false)
              }}
              className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg"
            >
              Clear All
            </button>
          </div>
        </div>
      )}

      {/* Column Selector Dropdown - Mobile */}
      {showColumnSelector && (
        <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4">
          <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Show Columns</div>
          {Object.entries(visibleColumns).map(([key, visible]) => (
            <label key={key} className="flex items-center space-x-2 py-1">
              <input
                type="checkbox"
                checked={visible}
                onChange={(e) => setVisibleColumns(prev => ({ ...prev, [key]: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700 capitalize">{key}</span>
            </label>
          ))}
        </div>
      )}

      {/* Density Dropdown - Mobile */}
      {showWindowList && (
        <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-2">
          {(['compact', 'comfortable', 'spacious'] as const).map((option) => (
            <button
              key={option}
              onClick={() => {
                setDensity(option)
                setShowWindowList(false)
              }}
              className={`w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 ${
                density === option ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'
              }`}
            >
              {option.charAt(0).toUpperCase() + option.slice(1)}
            </button>
          ))}
        </div>
      )}

      {/* Features Content */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading features...</p>
          </div>
        ) : filteredFeatures.length === 0 ? (
          <div className="p-6 text-center">
            <StarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No features found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm ? 'Try adjusting your search terms.' : 'Get started by creating your first feature.'}
            </p>
            <button
              onClick={handleCreateFeature}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Feature
            </button>
          </div>
        ) : (
          <div>
            {viewMode === 'list' && (
              <div className="overflow-hidden">
                {/* Bulk Actions Bar - Shows when features are selected */}
                {showBulkActions && selectedFeatures.length > 0 && (
                  <div className="bg-purple-50 border border-purple-200 rounded-lg px-4 py-2 shadow-sm">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                            <span className="text-purple-600 font-semibold text-xs">
                              {selectedFeatures.length}
                            </span>
                          </div>
                          <span className="text-xs font-medium text-purple-900">
                            {selectedFeatures.length === 1 ? 'feature' : 'features'} selected
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-1.5">
                          <button
                            onClick={handleBulkActivate}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 border border-green-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                            title="Activate selected features"
                          >
                            <EyeIcon className="h-3 w-3 mr-1" />
                            Activate
                          </button>
                          
                          <button
                            onClick={handleBulkDeactivate}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500"
                            title="Deactivate selected features"
                          >
                            <EyeSlashIcon className="h-3 w-3 mr-1" />
                            Deactivate
                          </button>
                          
                          <button
                            onClick={() => showBulkDeleteConfirmation(selectedFeatures)}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 border border-red-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500"
                            title="Delete selected features"
                          >
                            <TrashIcon className="h-3 w-3 mr-1" />
                            Delete
                          </button>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-1.5">
                        <button
                          onClick={handleClearSelection}
                          className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gray-500"
                          title="Clear selection"
                        >
                          <XMarkIcon className="h-3 w-3 mr-1" />
                          Clear
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                <div className={showBulkActions && selectedFeatures.length > 0 ? "mt-3" : ""}>
                  <table className="min-w-full">
                  <thead className="bg-gray-200 border-b border-gray-300">
                    <tr>
                      <th scope="col" className="relative pl-2 py-2" style={{ width: '6px' }}>
                        <input
                          type="checkbox"
                          checked={selectedFeatures.length === filteredFeatures.length && filteredFeatures.length > 0}
                          onChange={handleSelectAll}
                          className={`text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${
                            density === 'compact' 
                              ? 'h-4 w-4' 
                              : density === 'spacious' 
                              ? 'h-5 w-5' 
                              : 'h-4 w-4'
                          }`}
                        />
                      </th>
                      {visibleColumns.name && (
                        <th scope="col" className="pl-6 pr-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('name')}>
                          <div className="flex items-center space-x-1">
                            <span>Feature</span>
                            {getSortIcon('name')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.description && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('description')}>
                          <div className="flex items-center space-x-1">
                            <span>Description</span>
                            {getSortIcon('description')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.status && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('isIncluded')}>
                          <div className="flex items-center space-x-1">
                            <span>Status</span>
                            {getSortIcon('isIncluded')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.price && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('price')}>
                          <div className="flex items-center space-x-1">
                            <span>Price</span>
                            {getSortIcon('price')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.discountRate && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('discountRate')}>
                          <div className="flex items-center space-x-1">
                            <span>Discount Rate</span>
                            {getSortIcon('discountRate')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.totalDiscount && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('totalDiscount')}>
                          <div className="flex items-center space-x-1">
                            <span>Total Discount</span>
                            {getSortIcon('totalDiscount')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.actions && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider py-2 text-xs">
                          <span>Actions</span>
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                    {paginationData.paginatedFeatures.map((feature) => (
                      <FeatureRow
                        key={feature.id}
                        feature={feature}
                        isSelected={selectedFeature?.id === feature.id}
                        onFeatureSelect={handleFeatureSelect}
                        onEdit={handleEditFeature}
                        onDelete={handleDeleteFeature}
                        isChecked={selectedFeatures.includes(feature.id)}
                        onCheck={handleSelectFeature}
                        density={density}
                        visibleColumns={visibleColumns}
                        getFeatureIcon={getFeatureIcon}
                      />
                    ))}
                  </tbody>
                </table>
                </div>
              </div>
            )}

            {viewMode === 'grid' && (
              <div>
                {/* Bulk Actions Bar - Shows when features are selected */}
                {showBulkActions && selectedFeatures.length > 0 && (
                  <div className="bg-purple-50 border border-purple-200 rounded-lg px-4 py-2 shadow-sm">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                            <span className="text-purple-600 font-semibold text-xs">
                              {selectedFeatures.length}
                            </span>
                          </div>
                          <span className="text-xs font-medium text-purple-900">
                            {selectedFeatures.length === 1 ? 'feature' : 'features'} selected
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-1.5">
                          <button
                            onClick={handleBulkActivate}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 border border-green-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                            title="Activate selected features"
                          >
                            <EyeIcon className="h-3 w-3 mr-1" />
                            Activate
                          </button>
                          
                          <button
                            onClick={handleBulkDeactivate}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500"
                            title="Deactivate selected features"
                          >
                            <EyeSlashIcon className="h-3 w-3 mr-1" />
                            Deactivate
                          </button>
                          
                          <button
                            onClick={() => showBulkDeleteConfirmation(selectedFeatures)}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 border border-red-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500"
                            title="Delete selected features"
                          >
                            <TrashIcon className="h-3 w-3 mr-1" />
                            Delete
                          </button>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-1.5">
                        <button
                          onClick={handleClearSelection}
                          className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gray-500"
                          title="Clear selection"
                        >
                          <XMarkIcon className="h-3 w-3 mr-1" />
                          Clear
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                <div className={showBulkActions && selectedFeatures.length > 0 ? "mt-3" : ""}>
                  <div className={`grid gap-4 ${
                  gridColumns === 1 ? 'grid-cols-1' :
                  gridColumns === 2 ? 'grid-cols-1 md:grid-cols-2' :
                  gridColumns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
                  'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                }`}>
                    {paginationData.paginatedFeatures.map((feature) => 
                      renderFeatureCard(feature, false)
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>






      {/* Responsive Pagination */}
      {filteredFeatures.length > 0 && (
        <ResponsivePagination
          currentPage={currentPage}
          totalPages={paginationData.totalPages}
          onPageChange={handlePageChange}
          itemsPerPage={itemsPerPage}
          onItemsPerPageChange={handleItemsPerPageChange}
          totalItems={filteredFeatures.length}
          startIndex={paginationData.startIndex}
          endIndex={paginationData.endIndex}
          itemsPerPageOptions={[5, 10, 20, 50]}
          showItemsPerPage={true}
          showPageInfo={true}
        />
      )}

      {/* Modals */}
      <FeatureFormModal
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSubmit={handleSubmitForm}
        editingFeature={editingFeature}
        optionId={option.id}
        title={editingFeature ? 'Edit Feature' : 'New Feature'}
        subtitle="Feature management"
      />
      

      {/* Confirmation Modal for Delete Operations */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        title={isBulkDelete ? (bulkFeaturesToDelete.length === 1 ? "Delete Feature" : "Delete Multiple Features") : "Delete Feature"}
        message={(() => {
          if (isBulkDelete) {
            const count = bulkFeaturesToDelete.length
            if (verificationData?.canDelete) {
              return count === 1 
                ? `Are you sure you want to delete "${bulkFeaturesToDelete[0].name}"?`
                : `Are you sure you want to delete ${count} features?`
            } else {
              return count === 1 
                ? `Cannot delete "${bulkFeaturesToDelete[0].name}"`
                : `Cannot delete ${count} features`
            }
          }
          
          if (!verificationData) return `Are you sure you want to delete "${featureToDelete?.name}"?`
          
          if (verificationData.canDelete) {
            const hasOrders = (verificationData.orderDetails || 0) > 0
            
            if (hasOrders) {
              return `Delete "${featureToDelete?.name}" (has order history)?`
            } else {
              return `Delete "${featureToDelete?.name}"?`
            }
          } else {
            return `Cannot delete "${featureToDelete?.name}"`
          }
        })()}
        details={(() => {
          if (isBulkDelete) {
            const count = bulkFeaturesToDelete.length
            if (verificationData?.canDelete) {
              return `This will permanently remove ${count} ${count === 1 ? 'feature' : 'features'} and all associated data. This action cannot be undone.`
            } else {
              const totalOrderDetails = verificationData?.orderDetails || 0
              return `Some features have dependencies. Only features without orders will be deleted. This will skip ${totalOrderDetails} dependencies.`
            }
          }
          
          if (!verificationData) return "This action cannot be undone. The feature will be permanently removed from this option."
          
          if (verificationData.canDelete) {
            const hasOrders = (verificationData.orderDetails || 0) > 0
            
            if (hasOrders) {
              return "This feature has order history. Deleting it will remove all associated order records. This action cannot be undone."
            } else {
              return "This feature has no dependencies. It will be permanently removed from this option. This action cannot be undone."
            }
          } else {
            return "Please remove all dependencies before attempting to delete this feature."
          }
        })()}
        confirmText={verificationData?.canDelete || isBulkDelete ? "Delete" : "Delete Safe Only"}
        cancelText="Cancel"
        onConfirm={confirmDeleteFeature}
        onCancel={() => {
          setIsDeleteModalOpen(false)
          setFeatureToDelete(null)
          setBulkFeaturesToDelete([])
          setIsBulkDelete(false)
          setVerificationData(null)
          // Clear any remaining loading notifications when modal is cancelled
          clearLoadingNotifications()
        }}
        type={verificationData?.canDelete || isBulkDelete ? 'danger' : 'verification'}
        showVerification={true}
        verificationData={verificationData ? {
          canDelete: verificationData.canDelete,
          reason: verificationData.reason,
          dependencies: verificationData.dependencies
        } : undefined}
      />
    </div>
  )
}
