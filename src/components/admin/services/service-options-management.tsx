'use client'

// Imports
import { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { motion } from 'framer-motion'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ListBulletIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  Squares2X2Icon,
  XMarkIcon,
  ChevronDownIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ViewColumnsIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline'
import { CategoryHeader } from './category-header'
import { ConfirmationModal } from '../shared/confirmation-modal'
import { ResponsivePagination } from '../shared/responsive-pagination'
import { OptionFormModal } from './option-form-modal'

// Types
interface Service {
  id: string
  categoryId: string
  name: string
  description: string
  iconClass?: string
  price: number
  discountRate?: number
  totalDiscount?: number
  manager?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  category?: {
    id: string
    name: string
  }
  _count?: {
    serviceOptions: number
    orderDetails: number
  }
}

interface ServiceOption {
  id: string
  serviceId: string
  name: string
  description?: string
  price?: number
  discountRate?: number
  totalDiscount?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  service?: {
    id: string
    name: string
    category?: {
      id: string
      name: string
    }
  }
  _count?: {
    features: number
    orderDetails: number
  }
}

interface ServiceOptionsManagementProps {
  service: Service
  selectedOption: ServiceOption | null
  onOptionSelect: (option: ServiceOption | null) => void
  showSuccess: (title: string, message: string) => void
  showError: (title: string, message: string) => void
  showWarning: (title: string, message: string) => void
  showInfo: (title: string, message: string) => void
  showLoading: (title: string, message: string) => string
  clearLoadingNotifications: () => void
}

interface ServiceOptionFormData {
  name: string
  description: string
  price: number
  discountRate: number
  totalDiscount: number
  isActive: boolean
}


export function ServiceOptionsManagement({ 
  service, 
  selectedOption, 
  onOptionSelect,
  showSuccess,
  showError,
  showWarning,
  showInfo,
  showLoading,
  clearLoadingNotifications
}: ServiceOptionsManagementProps) {
  // State Management - Core data and UI state
  const [options, setOptions] = useState<ServiceOption[]>([])
  const [filteredOptions, setFilteredOptions] = useState<ServiceOption[]>([])
  const [loading, setLoading] = useState(true)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingOption, setEditingOption] = useState<ServiceOption | null>(null)
  const [selectedOptions, setSelectedOptions] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  
  // View and display state
  const [viewMode, setViewMode] = useState<'table' | 'list' | 'grid' | 'card'>('grid')
  const [density, setDensity] = useState<'compact' | 'comfortable' | 'spacious'>('compact')
  const [gridColumns, setGridColumns] = useState<number>(3)
  const [visibleColumns, setVisibleColumns] = useState<{
    name: boolean
    description: boolean
    price: boolean
    discountRate: boolean
    totalDiscount: boolean
    features: boolean
    status: boolean
    actions: boolean
  }>({
    name: true,
    description: true,
    price: true,
    discountRate: true,
    totalDiscount: true,
    features: true,
    status: true,
    actions: true
  })
  
  // UI interaction state
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnSelector, setShowColumnSelector] = useState(false)
  const [showWindowList, setShowWindowList] = useState(false)
  const [currentFilters, setCurrentFilters] = useState<{
    status?: string
    priceRange?: string
    [key: string]: string | undefined
  }>({})
  const [showBulkActions, setShowBulkActions] = useState(false)
  
  // Sorting and pagination state
  const [sortField, setSortField] = useState<string>('')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  
  // Modal and confirmation state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [optionToDelete, setOptionToDelete] = useState<ServiceOption | null>(null)
  const [isBulkDelete, setIsBulkDelete] = useState(false)
  const [bulkOptionsToDelete, setBulkOptionsToDelete] = useState<ServiceOption[]>([])
  const [verificationData, setVerificationData] = useState<{
    canDelete: boolean
    reason?: string
    features?: number
    orderDetails?: number
    dependencies?: string[]
  } | null>(null)
  
  // Layout and spacing state
  const [dropdownSpaceNeeded, setDropdownSpaceNeeded] = useState(0)
  

  // Utility Functions - Icon mapping and helper functions
  const getOptionIcon = useCallback((option: ServiceOption): string => {
    const name = option.name.toLowerCase()

    // Icon mapping based on option name patterns - helps users quickly identify option types
    if (name.includes('basic') || name.includes('starter') || name.includes('essential')) {
      return 'fa-layer-group text-blue-500'
    }
    if (name.includes('premium') || name.includes('pro') || name.includes('professional') || name.includes('advanced')) {
      return 'fa-crown text-yellow-500'
    }
    if (name.includes('enterprise') || name.includes('business') || name.includes('corporate')) {
      return 'fa-building text-purple-500'
    }
    if (name.includes('custom') || name.includes('bespoke') || name.includes('tailored')) {
      return 'fa-magic text-pink-500'
    }
    if (name.includes('maintenance') || name.includes('support') || name.includes('care')) {
      return 'fa-tools text-orange-500'
    }
    if (name.includes('hosting') || name.includes('domain') || name.includes('server')) {
      return 'fa-server text-gray-500'
    }
    if (name.includes('seo') || name.includes('marketing') || name.includes('promotion')) {
      return 'fa-search text-green-500'
    }
    if (name.includes('security') || name.includes('ssl') || name.includes('protection')) {
      return 'fa-shield-alt text-red-500'
    }
    if (name.includes('analytics') || name.includes('tracking') || name.includes('monitoring')) {
      return 'fa-chart-bar text-teal-500'
    }
    
    // Default fallback icon
    return 'fa-list-ul text-orange-500'
  }, [])

  // Helper functions for UI configuration
  const getDefaultColumns = useCallback((density: 'compact' | 'comfortable' | 'spacious') => {
    return {
      name: true,
      description: true,
      price: true,
      discountRate: true,
      totalDiscount: true,
      features: true,
      status: true,
      actions: true
    }
  }, [])

  const handleDensityChange = useCallback((newDensity: 'compact' | 'comfortable' | 'spacious') => {
    setDensity(newDensity)
    setVisibleColumns(getDefaultColumns(newDensity))
  }, [getDefaultColumns])

  // Handlers - Selection and bulk operations
  const handleSelectOption = useCallback((optionId: string) => {
    setSelectedOptions(prev => {
      const newSelection = prev.includes(optionId) 
        ? prev.filter(id => id !== optionId)
        : [...prev, optionId]
      setShowBulkActions(newSelection.length > 0)
      return newSelection
    })
  }, [])

  const handleSelectAll = useCallback(() => {
    if (selectedOptions.length === filteredOptions.length) {
      setSelectedOptions([])
      setShowBulkActions(false)
    } else {
      setSelectedOptions(filteredOptions.map(option => option.id))
      setShowBulkActions(true)
    }
  }, [selectedOptions.length, filteredOptions])

  // Handlers - Bulk operations for multiple options
  const handleBulkActivate = useCallback(async () => {
    if (selectedOptions.length === 0) return
    
    try {
      showLoading('Activating Options', `Activating ${selectedOptions.length} option${selectedOptions.length === 1 ? '' : 's'}...`)
      
      // Process all selected options in parallel for better performance
      const promises = selectedOptions.map(optionId => {
        const option = options.find(o => o.id === optionId)
        if (!option) return Promise.resolve()
        
        return fetch(`/api/admin/service-options/${optionId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: option.name,
            description: option.description,
            serviceid: option.serviceId,
            price: option.price,
            discountrate: option.discountRate,
            totaldiscount: option.totalDiscount,
            isactive: true
          })
        })
      })
      
      await Promise.all(promises)
      
      // Update both options arrays to maintain consistency
      setOptions(prev => prev.map(option => 
        selectedOptions.includes(option.id) 
          ? { ...option, isActive: true }
          : option
      ))
      setFilteredOptions(prev => prev.map(option => 
        selectedOptions.includes(option.id) 
          ? { ...option, isActive: true }
          : option
      ))
      
      showSuccess('Options Activated', `Successfully activated ${selectedOptions.length} option${selectedOptions.length === 1 ? '' : 's'}.`)
      setSelectedOptions([])
      setShowBulkActions(false)
    } catch (error) {
      console.error('Error activating options:', error)
      showError('Activation Failed', 'Failed to activate selected options')
    }
  }, [selectedOptions, options, showLoading, showSuccess, showError])

  const handleBulkDeactivate = useCallback(async () => {
    if (selectedOptions.length === 0) return
    
    try {
      showLoading('Deactivating Options', `Deactivating ${selectedOptions.length} option${selectedOptions.length === 1 ? '' : 's'}...`)
      
      // Process all selected options in parallel
      const promises = selectedOptions.map(optionId => {
        const option = options.find(o => o.id === optionId)
        if (!option) return Promise.resolve()
        
        return fetch(`/api/admin/service-options/${optionId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: option.name,
            description: option.description,
            serviceid: option.serviceId,
            price: option.price,
            discountrate: option.discountRate,
            totaldiscount: option.totalDiscount,
            isactive: false
          })
        })
      })
      
      await Promise.all(promises)
      
      // Update both options arrays to maintain consistency
      setOptions(prev => prev.map(option => 
        selectedOptions.includes(option.id) 
          ? { ...option, isActive: false }
          : option
      ))
      setFilteredOptions(prev => prev.map(option => 
        selectedOptions.includes(option.id) 
          ? { ...option, isActive: false }
          : option
      ))
      
      showSuccess('Options Deactivated', `Successfully deactivated ${selectedOptions.length} option${selectedOptions.length === 1 ? '' : 's'}.`)
      setSelectedOptions([])
      setShowBulkActions(false)
    } catch (error) {
      console.error('Error deactivating options:', error)
      showError('Deactivation Failed', 'Failed to deactivate selected options')
    }
  }, [selectedOptions, options, showLoading, showSuccess, showError])

  // Show confirmation modal for bulk delete with dependency verification
  const showBulkDeleteConfirmation = useCallback(async (optionIds: string[]) => {
    const count = optionIds.length
    const optionsToDelete = options.filter(option => optionIds.includes(option.id))
    
    try {
      showLoading('Verifying Options', `Checking dependencies for ${optionsToDelete.length} options...`)
      
      // Check dependencies for each option
      const verificationPromises = optionsToDelete.map(async (option) => {
        try {
          const response = await fetch(`/api/admin/service-options/${option.id}`)
          if (response.ok) {
            const optionData = await response.json()
            const freshOption = optionData.data
            
            const featuresCount = freshOption._count?.features || 0
            const orderDetailsCount = freshOption._count?.orderDetails || 0
            
            return {
              option,
              canDelete: featuresCount === 0 && orderDetailsCount === 0,
              features: featuresCount,
              orderDetails: orderDetailsCount,
              dependencies: [
                ...(featuresCount > 0 ? [`${featuresCount} feature${featuresCount === 1 ? '' : 's'}`] : []),
                ...(orderDetailsCount > 0 ? [`${orderDetailsCount} order${orderDetailsCount === 1 ? '' : 's'}`] : [])
              ]
            }
          }
          return {
            option,
            canDelete: false,
            features: 0,
            orderDetails: 0,
            dependencies: ['Unable to verify dependencies']
          }
        } catch (error) {
          return {
            option,
            canDelete: false,
            features: 0,
            orderDetails: 0,
            dependencies: ['Verification failed']
          }
        }
      })
      
      const verificationResults = await Promise.all(verificationPromises)
      
      // Calculate overall verification data
      const totalFeatures = verificationResults.reduce((sum, result) => sum + result.features, 0)
      const totalOrderDetails = verificationResults.reduce((sum, result) => sum + result.orderDetails, 0)
      const canDeleteAll = verificationResults.every(result => result.canDelete)
      const safeToDelete = verificationResults.filter(result => result.canDelete)
      const unsafeToDelete = verificationResults.filter(result => !result.canDelete)
      
      let reason = ''
      let dependencies: string[] = []
      
      if (canDeleteAll) {
        reason = 'All selected options are safe to delete'
        dependencies = []
      } else {
        reason = `${unsafeToDelete.length} of ${optionsToDelete.length} options have dependencies`
        dependencies = [
          `${totalFeatures} total feature${totalFeatures === 1 ? '' : 's'}`,
          `${totalOrderDetails} total order${totalOrderDetails === 1 ? '' : 's'}`
        ]
      }
      
      setBulkOptionsToDelete(optionsToDelete)
      setIsBulkDelete(true)
      setVerificationData({
        canDelete: canDeleteAll,
        reason,
        features: totalFeatures,
        orderDetails: totalOrderDetails,
        dependencies
      })
      setIsDeleteModalOpen(true)
      
    } catch (error) {
      console.error('Error verifying bulk delete:', error)
      showError('Verification Error', 'Failed to verify option dependencies.')
    }
  }, [selectedOptions, options, showLoading, showError])

  const handleClearSelection = useCallback(() => {
    setSelectedOptions([])
    setShowBulkActions(false)
  }, [])

  // Handlers - Sorting and UI interactions
  const handleSort = useCallback((field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }, [sortField, sortDirection])

  const getSortIcon = useCallback((field: string) => {
    if (sortField !== field) {
      return <ArrowUpIcon className="h-3 w-3 text-gray-300" />
    }
    return sortDirection === 'asc' 
      ? <ArrowUpIcon className="h-3 w-3 text-amber-600" />
      : <ArrowDownIcon className="h-3 w-3 text-amber-600" />
  }, [sortField, sortDirection])

  // Components - OptionRow for table view
  const OptionRow = useCallback(({ 
    option, 
    isSelected, 
    onOptionSelect, 
    onEdit, 
    onDelete,
    isChecked,
    onCheck
  }: {
    option: ServiceOption
    isSelected: boolean
    onOptionSelect: (option: ServiceOption) => void
    onEdit: (option: ServiceOption) => void
    onDelete: (option: ServiceOption) => void
    isChecked: boolean
    onCheck: (optionId: string) => void
  }) => {
    const isCurrentlySelected = isSelected

    return (
      <tr
        className={`hover:bg-gray-50 cursor-pointer ${
          isCurrentlySelected ? 'bg-amber-50' : ''
        } ${
          isChecked ? 'bg-amber-50 border-l-4 border-amber-500' : ''
        } ${
          density === 'compact' 
            ? 'py-0' 
            : density === 'spacious' 
            ? 'py-4' 
            : 'py-2'
        }`}
        onClick={() => onOptionSelect(option)}
      >
        <td className={`pl-2 whitespace-nowrap ${
          density === 'compact' 
            ? 'py-0' 
            : density === 'spacious' 
            ? 'py-4' 
            : 'py-2'
        }`} style={{ width: '6px' }}>
          <input
            type="checkbox"
            checked={isChecked}
            onChange={() => onCheck(option.id)}
            onClick={(e) => e.stopPropagation()}
            className={`text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${
              density === 'compact' 
                ? 'h-4 w-4' 
                : density === 'spacious' 
                ? 'h-5 w-5' 
                : 'h-4 w-4'
            }`}
          />
        </td>
        {visibleColumns.name && (
          <td className={`pl-4 pr-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <div className="flex items-center">
              {/* Option Icon */}
              <div className={`flex items-center justify-center mr-1 ${
                density === 'compact' 
                  ? 'w-8 h-8' 
                  : density === 'spacious' 
                  ? 'w-10 h-10' 
                  : 'w-8 h-8'
              }`}>
                <i className={`fas ${getOptionIcon(option)} ${
                  density === 'compact' 
                    ? 'text-lg' 
                    : density === 'spacious' 
                    ? 'text-xl' 
                    : 'text-lg'
                }`}></i>
              </div>

              {/* Option Name */}
              <div className="flex-1 min-w-0">
                <span className={`text-gray-500 truncate font-bold ${
                  density === 'compact' 
                    ? 'text-sm' 
                    : density === 'spacious' 
                    ? 'text-base' 
                    : 'text-sm'
                }`}>
                  {option.name}
                </span>
              </div>
            </div>
          </td>
        )}

        {visibleColumns.description && (
          <td className={`px-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`text-gray-500 truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            }`}>
              {option.description || 'No description'}
            </span>
          </td>
        )}

        {visibleColumns.price && (
          <td className={`px-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`text-gray-500 truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            }`}>
              ${option.price || 0}
            </span>
          </td>
        )}

        {visibleColumns.discountRate && (
          <td className={`px-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`text-gray-500 truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            }`}>
              {option.discountRate || 0}%
            </span>
          </td>
        )}
        {visibleColumns.totalDiscount && (
          <td className={`px-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            } ${
              (() => {
                // Calculate discount amount for styling
                const basePrice = option.price || 0;
                const discountRate = option.discountRate || 0;
                const calculatedDiscount = basePrice * (discountRate / 100);
                return calculatedDiscount > 0 ? 'text-red-600 font-medium' : 'text-gray-500';
              })()
            }`}>
              {(() => {
                // Calculate and display discount amount
                const basePrice = option.price || 0;
                const discountRate = option.discountRate || 0;
                const calculatedDiscount = basePrice * (discountRate / 100);
                return calculatedDiscount > 0 ? `-$${calculatedDiscount.toFixed(2)}` : '$0';
              })()}
            </span>
          </td>
        )}

        {visibleColumns.features && (
          <td className={`px-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`text-gray-500 truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            }`}>
              {option._count?.features || 0}
            </span>
          </td>
        )}

        {visibleColumns.status && (
          <td className={`px-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`text-gray-500 truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            }`}>
              {option.isActive ? 'Active' : 'Inactive'}
            </span>
          </td>
        )}

        {visibleColumns.actions && (
          <td className={`px-6 whitespace-nowrap text-right text-sm font-medium ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <div className="flex items-center justify-start space-x-2">
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onEdit(option)
                }}
                className="text-blue-600 hover:text-blue-900 p-1 hover:scale-110"
                title="Edit option"
              >
                <PencilIcon className={`${
                  density === 'compact' 
                    ? 'h-4 w-4' 
                    : density === 'spacious' 
                    ? 'h-5 w-5' 
                    : 'h-4 w-4'
                }`} />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete(option)
                }}
                className="text-red-600 hover:text-red-900 p-1 hover:scale-110"
                title="Delete option"
              >
                <TrashIcon className={`${
                  density === 'compact' 
                    ? 'h-4 w-4' 
                    : density === 'spacious' 
                    ? 'h-5 w-5' 
                    : 'h-4 w-4'
                }`} />
              </button>
            </div>
          </td>
        )}
      </tr>
    )
  }, [density, visibleColumns, getOptionIcon])

  // Form state for option creation/editing
  const [formData, setFormData] = useState<ServiceOptionFormData>({
    name: '',
    description: '',
    price: 0,
    discountRate: 0,
    totalDiscount: 0,
    isActive: true
  })

  // Effects - Search debouncing and data fetching
  useEffect(() => {
    // Debounce search term to avoid excessive filtering
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchTerm])

  useEffect(() => {
    // Fetch options when service changes
    fetchOptions()
  }, [service.id])

  // Effects - Filtering and sorting options
  useEffect(() => {
    let filtered = options

    // Apply search filter - search in name and description
    if (debouncedSearchTerm) {
      filtered = filtered.filter(option =>
        option.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
        option.description?.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
      )
    }

    // Apply status filter
    if (currentFilters.status) {
      filtered = filtered.filter(option => {
        if (currentFilters.status === 'active') return option.isActive
        if (currentFilters.status === 'inactive') return !option.isActive
        return true
      })
    }

    // Apply price range filter
    if (currentFilters.priceRange) {
      filtered = filtered.filter(option => {
        const price = option.price || 0
        if (currentFilters.priceRange === 'free') return price === 0
        if (currentFilters.priceRange === 'low') return price > 0 && price <= 100
        if (currentFilters.priceRange === 'medium') return price > 100 && price <= 500
        if (currentFilters.priceRange === 'high') return price > 500
        return true
      })
    }

    // Apply sorting with proper type handling
    if (sortField) {
      filtered.sort((a, b) => {
        let aValue: any = a[sortField as keyof ServiceOption]
        let bValue: any = b[sortField as keyof ServiceOption]

        // Handle different field types for proper sorting
        if (sortField === 'name' || sortField === 'description') {
          aValue = (aValue || '').toLowerCase()
          bValue = (bValue || '').toLowerCase()
        } else if (sortField === 'isActive') {
          aValue = aValue ? 1 : 0
          bValue = bValue ? 1 : 0
        } else if (sortField === 'price' || sortField === 'discountRate' || sortField === 'totalDiscount') {
          aValue = aValue || 0
          bValue = bValue || 0
        } else if (sortField === 'features') {
          aValue = a._count?.features || 0
          bValue = b._count?.features || 0
        } else if (sortField === 'createdAt' || sortField === 'updatedAt') {
          aValue = new Date(aValue).getTime()
          bValue = new Date(bValue).getTime()
        }

        // Apply sort direction
        if (sortDirection === 'asc') {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
        } else {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
        }
      })
    }

    setFilteredOptions(filtered)
    setCurrentPage(1) // Reset to first page when filtering or sorting changes
  }, [options, debouncedSearchTerm, currentFilters, sortField, sortDirection])
  
  // Computed values - Pagination calculations
  const totalPages = useMemo(() => Math.ceil(filteredOptions.length / itemsPerPage), [filteredOptions.length, itemsPerPage])
  const startIndex = useMemo(() => (currentPage - 1) * itemsPerPage, [currentPage, itemsPerPage])
  const endIndex = useMemo(() => startIndex + itemsPerPage, [startIndex, itemsPerPage])
  const paginatedOptions = useMemo(() => filteredOptions.slice(startIndex, endIndex), [filteredOptions, startIndex, endIndex])
  
  // Handlers - Pagination controls
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page)
  }, [])
  
  const handleItemsPerPageChange = useCallback((items: number) => {
    setItemsPerPage(items)
    setCurrentPage(1) // Reset to first page when changing items per page
  }, [])
  
  // Effects - UI layout and interaction management
  useEffect(() => {
    // Calculate space needed for dropdowns to prevent layout shifts
    let spaceNeeded = 0
    
    if (showColumnSelector) {
      // Column selector dropdown height: ~200px
      spaceNeeded = Math.max(spaceNeeded, 200)
    }
    
    if (showWindowList) {
      // Density dropdown height: ~120px
      spaceNeeded = Math.max(spaceNeeded, 120)
    }
    
    if (showFilters) {
      // Filters dropdown height: ~300px
      spaceNeeded = Math.max(spaceNeeded, 300)
    }
    
    setDropdownSpaceNeeded(spaceNeeded)
  }, [showColumnSelector, showWindowList, showFilters])

  useEffect(() => {
    // Handle click outside to close dropdowns
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('.dropdown-container')) {
        setShowFilters(false)
        setShowColumnSelector(false)
        setShowWindowList(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // API Functions - Data fetching and CRUD operations
  const fetchOptions = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/service-options?serviceId=${service.id}&limit=100`)
      
      if (response.ok) {
        const data = await response.json()
        const optionsData = data.data || []
        console.log('Fetched service options:', optionsData)
        setOptions(optionsData)
        setFilteredOptions(optionsData)
        
        if (optionsData.length > 0) {
          showSuccess('Options Loaded', `Loaded ${optionsData.length} service option${optionsData.length === 1 ? '' : 's'}`)
        } else {
          showInfo('No Options Found', 'This service has no options yet. Create the first one!')
        }
      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('Failed to fetch options:', response.status, response.statusText, errorData)
        setOptions([])
        setFilteredOptions([])
      }
    } catch (error) {
      console.error('Error fetching options:', error)
      setOptions([])
      setFilteredOptions([])
    } finally {
      setLoading(false)
    }
  }, [service.id, showSuccess, showInfo])

  // Handlers - Option selection and form management
  const handleOptionSelect = useCallback((option: ServiceOption) => {
    onOptionSelect(option)
  }, [onOptionSelect])

  const handleCreateOption = useCallback(() => {
    setIsFormOpen(true)
    setEditingOption(null)
    setFormData({
      name: '',
      description: '',
      price: 0,
      discountRate: 0,
      totalDiscount: 0,
      isActive: true
    })
    console.log('showInfo function:', showInfo)
    showInfo('Create Option', 'Fill out the form below to create a new service option.')
  }, [showInfo])

  const handleEditOption = useCallback((option: ServiceOption) => {
    setEditingOption(option)
    setFormData({
      name: option.name,
      description: option.description || '',
      price: option.price || 0,
      discountRate: option.discountRate || 0,
      totalDiscount: option.totalDiscount || 0,
      isActive: option.isActive
    })
    setIsFormOpen(true)
    showInfo('Edit Option', `Editing service option: "${option.name}"`)
  }, [showInfo])

  // API Functions - Dependency checking for safe deletion
  const checkOptionDependencies = useCallback(async (option: ServiceOption) => {
    try {
      // Fetch fresh data from API to get current dependency counts
      const response = await fetch(`/api/admin/service-options/${option.id}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch option details')
      }
      
      const data = await response.json()
      const freshOption = data.data
      
      // Check for features and order details using fresh data
      const featuresCount = freshOption._count?.features || 0
      const orderDetailsCount = freshOption._count?.orderDetails || 0
      
      const dependencies: string[] = []
      let canDelete = true
      let reason = ''
      
      // Check for blocking dependencies
      if (featuresCount > 0) {
        dependencies.push(`${featuresCount} feature${featuresCount === 1 ? '' : 's'}`)
        canDelete = false
        reason = 'Option has associated features that must be removed first'
      }
      
      if (orderDetailsCount > 0) {
        dependencies.push(`${orderDetailsCount} order${orderDetailsCount === 1 ? '' : 's'}`)
        canDelete = false
        reason = 'Option is associated with existing orders'
      }
      
      if (canDelete) {
        reason = 'No dependencies found - safe to delete'
      }
      
      setVerificationData({
        canDelete,
        reason,
        features: featuresCount,
        orderDetails: orderDetailsCount,
        dependencies
      })
      
    } catch (error) {
      console.error('Error checking option dependencies:', error)
      setVerificationData({
        canDelete: false,
        reason: 'Error checking dependencies',
        dependencies: ['Unable to verify dependencies']
      })
    }
  }, [])

  const handleDeleteOption = useCallback(async (option: ServiceOption) => {
    setOptionToDelete(option)
    showLoading('Verifying Option', `Checking if "${option.name}" can be deleted...`)
    await checkOptionDependencies(option)
    setIsDeleteModalOpen(true)
  }, [checkOptionDependencies, showLoading])

  // Execute bulk delete after confirmation
  const executeBulkDelete = useCallback(async () => {
    if (!bulkOptionsToDelete.length) return

    try {
      // If not all options can be deleted, only delete the safe ones
      if (!verificationData?.canDelete) {
        showLoading('Deleting Safe Options', 'Deleting only options without dependencies...')
        
        // Re-verify each option to get fresh data
        const verificationPromises = bulkOptionsToDelete.map(async (option) => {
          try {
            const response = await fetch(`/api/admin/service-options/${option.id}`)
            if (response.ok) {
              const optionData = await response.json()
              const freshOption = optionData.data
              const featuresCount = freshOption._count?.features || 0
              const orderDetailsCount = freshOption._count?.orderDetails || 0
              return {
                option,
                canDelete: featuresCount === 0 && orderDetailsCount === 0
              }
            }
            return { option, canDelete: false }
          } catch (error) {
            return { option, canDelete: false }
          }
        })
        
        const verificationResults = await Promise.all(verificationPromises)
        const safeToDelete = verificationResults.filter(result => result.canDelete)
        const unsafeToDelete = verificationResults.filter(result => !result.canDelete)
        
        if (safeToDelete.length === 0) {
          showWarning('No Safe Options', 'None of the selected options can be deleted due to dependencies.')
          setIsDeleteModalOpen(false)
          setBulkOptionsToDelete([])
          setIsBulkDelete(false)
          setVerificationData(null)
          return
        }
        
        // Delete only safe options
        const deletePromises = safeToDelete.map(result => 
          fetch(`/api/admin/service-options/${result.option.id}`, {
            method: 'DELETE',
          })
        )
        
        await Promise.all(deletePromises)
        
        let message = `Successfully deleted ${safeToDelete.length} options.`
        if (unsafeToDelete.length > 0) {
          message += ` ${unsafeToDelete.length} options were skipped due to dependencies.`
        }
        
        showSuccess('Options Deleted', message)
      } else {
        // All options are safe to delete
        showLoading('Deleting Options', `Deleting ${bulkOptionsToDelete.length} options...`)
        
        const promises = bulkOptionsToDelete.map(option => 
          fetch(`/api/admin/service-options/${option.id}`, { method: 'DELETE' })
        )
        
        await Promise.all(promises)
        showSuccess('Options Deleted', `Successfully deleted ${bulkOptionsToDelete.length} options.`)
      }
      
      // Remove deleted options from both arrays
      setOptions(prev => prev.filter(option => !bulkOptionsToDelete.map(o => o.id).includes(option.id)))
      setFilteredOptions(prev => prev.filter(option => !bulkOptionsToDelete.map(o => o.id).includes(option.id)))
      setSelectedOptions([])
      setShowBulkActions(false)
    } catch (error) {
      console.error('Error deleting options:', error)
      showError('Delete Error', 'Failed to delete selected options.')
    } finally {
      setIsDeleteModalOpen(false)
      setBulkOptionsToDelete([])
      setIsBulkDelete(false)
      setVerificationData(null)
    }
  }, [bulkOptionsToDelete, verificationData, showLoading, showSuccess, showWarning, showError])

  const confirmDeleteOption = useCallback(async () => {
    if (isBulkDelete) {
      await executeBulkDelete()
      return
    }

    if (!optionToDelete) return
    
    try {
      showLoading('Deleting Option', 'Removing service option from the system...')
      
      const response = await fetch(`/api/admin/service-options/${optionToDelete.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        // Remove deleted option from both arrays
        setOptions(prev => prev.filter(option => option.id !== optionToDelete.id))
        setFilteredOptions(prev => prev.filter(option => option.id !== optionToDelete.id))
        showSuccess('Option Deleted', `"${optionToDelete.name}" has been successfully removed.`)
      } else {
        const errorData = await response.json()
        showError('Delete Failed', errorData.message || 'Failed to delete service option')
      }
    } catch (error) {
      console.error('Error deleting option:', error)
      showError('Delete Failed', 'Failed to delete service option')
    } finally {
      setIsDeleteModalOpen(false)
      setOptionToDelete(null)
    }
  }, [isBulkDelete, optionToDelete, executeBulkDelete, showLoading, showSuccess, showError])

  // Components - Option card renderer for grid view
  const renderOptionCard = useCallback((option: ServiceOption, isLargeCard: boolean = false) => {
    const isSelected = selectedOption?.id === option.id

    // Apply density-based padding for consistent spacing
    const getDensityPadding = () => {
      if (isLargeCard) return 'p-5'
      switch (density) {
        case 'compact': return 'p-3'
        case 'comfortable': return 'p-4'
        case 'spacious': return 'p-5'
        default: return 'p-4'
      }
    }

    return (
      <div
        key={option.id}
        className={`group relative bg-white border border-gray-200 rounded-lg cursor-pointer overflow-hidden hover:cursor-pointer shadow-sm hover:shadow-md ${
          isSelected
            ? 'ring-2 ring-amber-500 ring-offset-2 bg-amber-50/50 border-amber-300'
            : 'hover:bg-gray-50/50 hover:border-gray-300'
        } ${
          selectedOptions.includes(option.id) 
            ? 'bg-amber-50 border-l-4 border-amber-500' 
            : ''
        } ${getDensityPadding()}`}
        onClick={() => handleOptionSelect(option)}
        data-section="options"
        onMouseEnter={(e) => {
          // Only show action menu on hover for desktop (lg and up)
          if (window.innerWidth >= 1024) {
            const actionMenu = e.currentTarget.querySelector('.action-menu') as HTMLElement;
            if (actionMenu) {
              actionMenu.style.opacity = '1';
              actionMenu.style.transform = 'translateX(0)';
              actionMenu.style.pointerEvents = 'auto';
            }
          }
        }}
        onMouseLeave={(e) => {
          // Only hide action menu on mouse leave for desktop (lg and up)
          if (window.innerWidth >= 1024) {
            const actionMenu = e.currentTarget.querySelector('.action-menu') as HTMLElement;
            if (actionMenu) {
              actionMenu.style.opacity = '0';
              actionMenu.style.transform = 'translateX(100%)';
              actionMenu.style.pointerEvents = 'none';
            }
          }
        }}
      >
        {/* Header Section */}
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-start space-x-2 flex-1 min-w-0">
            {/* Checkbox */}
            <div className="flex-shrink-0 pt-1" onClick={(e) => e.stopPropagation()}>
              <input
                type="checkbox"
                checked={selectedOptions.includes(option.id)}
                onChange={(e) => {
                  e.stopPropagation()
                  handleSelectOption(option.id)
                }}
                onClick={(e) => e.stopPropagation()}
                className={`h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 rounded ${
                  density === 'compact' 
                    ? 'h-3 w-3' 
                    : density === 'spacious' 
                    ? 'h-5 w-5' 
                    : 'h-4 w-4'
                }`}
              />
            </div>
            {/* Icon Container */}
            <div className="flex-shrink-0 p-2 bg-amber-50 rounded-lg">
              <i className={`fas ${getOptionIcon(option)} text-2xl`}></i>
            </div>
            
            {/* Title and Description */}
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-base text-gray-900 truncate mb-2">
                {option.name}
              </h3>
              {option.description && (
                <p className="text-sm text-gray-600 line-clamp-2 leading-relaxed">
                  {option.description}
                </p>
              )}
            </div>
          </div>

          {/* Status Badge */}
          <span className={`inline-flex items-center px-2.5 py-0 rounded-full text-xs font-medium ${
            option.isActive
              ? 'bg-green-100 text-green-800 border border-green-200'
              : 'bg-red-100 text-red-800 border border-red-200'
          }`}>
            <div className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
              option.isActive ? 'bg-green-400' : 'bg-red-400'
            }`}></div>
            {option.isActive ? 'Active' : 'Inactive'}
          </span>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-100 my-1"></div>

        {/* Pricing Information */}
        <div className={`${
          density === 'compact' ? 'space-y-2 mb-2' : density === 'spacious' ? 'space-y-4 mb-6' : 'space-y-3 mb-4'
        }`}>
          {/* Pricing Grid */}
          <div className="grid grid-cols-2 gap-3">
            {/* Base Price */}
            <div className={`bg-gray-50 rounded-lg ${
              density === 'compact' 
                ? 'p-2' 
                : density === 'spacious' 
                ? 'p-4' 
                : 'p-3'
            }`}>
              <div className="flex items-center space-x-2 mb-1">
                <div className={`bg-white rounded-lg flex items-center justify-center shadow-sm ${
                  density === 'compact' 
                    ? 'w-5 h-5' 
                    : density === 'spacious' 
                    ? 'w-8 h-8' 
                    : 'w-6 h-6'
                }`}>
                  <i className={`fas fa-dollar-sign text-green-600 ${
                    density === 'compact' 
                      ? 'text-xs' 
                      : density === 'spacious' 
                      ? 'text-sm' 
                      : 'text-xs'
                  }`}></i>
                </div>
                <div className={`font-medium text-gray-500 uppercase tracking-wide ${
                  density === 'compact' 
                    ? 'text-xs' 
                    : density === 'spacious' 
                    ? 'text-sm' 
                    : 'text-xs'
                }`}>
                  Base Price
                </div>
              </div>
              <div className={`font-bold text-gray-900 ${
                density === 'compact' 
                  ? 'text-sm' 
                  : density === 'spacious' 
                  ? 'text-lg' 
                  : 'text-base'
              }`}>
                ${option.price || 0}
            </div>
          </div>

            {/* Discount Rate */}
            <div className={`bg-gray-50 rounded-lg ${
              density === 'compact' 
                ? 'p-2' 
                : density === 'spacious' 
                ? 'p-4' 
                : 'p-3'
            }`}>
              <div className="flex items-center space-x-2 mb-1">
                <div className={`bg-white rounded-lg flex items-center justify-center shadow-sm ${
                  density === 'compact' 
                    ? 'w-5 h-5' 
                    : density === 'spacious' 
                    ? 'w-8 h-8' 
                    : 'w-6 h-6'
                }`}>
                  <i className={`fas fa-percentage text-blue-600 ${
                    density === 'compact' 
                      ? 'text-xs' 
                      : density === 'spacious' 
                      ? 'text-sm' 
                      : 'text-xs'
                  }`}></i>
                </div>
                <div className={`font-medium text-gray-500 uppercase tracking-wide ${
                  density === 'compact' 
                    ? 'text-xs' 
                    : density === 'spacious' 
                    ? 'text-sm' 
                    : 'text-xs'
                }`}>
                  Discount Rate
                </div>
              </div>
              <div className={`font-bold ${
                option.discountRate && option.discountRate > 0 
                  ? 'text-blue-600' 
                  : 'text-gray-400'
              } ${
                density === 'compact' 
                  ? 'text-sm' 
                  : density === 'spacious' 
                  ? 'text-lg' 
                  : 'text-base'
              }`}>
                {option.discountRate || 0}%
              </div>
            </div>
          </div>

          {/* Total Discount (if applicable) - Show only when discount exists */}
          {(() => {
            // Calculate discount amount for display
            const basePrice = option.price || 0;
            const discountRate = option.discountRate || 0;
            const calculatedDiscount = basePrice * (discountRate / 100);
            return calculatedDiscount > 0 && (
              <div className={`bg-red-50 rounded-lg border border-red-100 ${
                density === 'compact' 
                  ? 'p-2' 
                  : density === 'spacious' 
                  ? 'p-4' 
                  : 'p-3'
              }`}>
                <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
                    <div className={`bg-white rounded-lg flex items-center justify-center shadow-sm ${
                      density === 'compact' 
                        ? 'w-5 h-5' 
                        : density === 'spacious' 
                        ? 'w-8 h-8' 
                        : 'w-6 h-6'
                    }`}>
                      <i className={`fas fa-tag text-red-600 ${
                        density === 'compact' 
                          ? 'text-xs' 
                          : density === 'spacious' 
                          ? 'text-sm' 
                          : 'text-xs'
                      }`}></i>
            </div>
                    <div>
                      <div className={`font-medium text-gray-500 uppercase tracking-wide ${
                        density === 'compact' 
                          ? 'text-xs' 
                          : density === 'spacious' 
                          ? 'text-sm' 
                          : 'text-xs'
                      }`}>
                        Total Discount
                      </div>
                      <div className={`font-bold text-red-600 ${
                        density === 'compact' 
                          ? 'text-sm' 
                          : density === 'spacious' 
                          ? 'text-lg' 
                          : 'text-base'
                      }`}>
                        -${calculatedDiscount.toFixed(2)}
                      </div>
                    </div>
                  </div>
                  {/* Final Price Calculation - Shows price after discount */}
                  <div className="text-right">
                    <div className={`font-medium text-gray-500 uppercase tracking-wide ${
                      density === 'compact' 
                        ? 'text-xs' 
                        : density === 'spacious' 
                        ? 'text-sm' 
                        : 'text-xs'
                    }`}>
                      Final Price
                    </div>
                    <div className={`font-bold text-green-600 ${
                      density === 'compact' 
                        ? 'text-sm' 
                        : density === 'spacious' 
                        ? 'text-lg' 
                        : 'text-base'
                    }`}>
                      ${(basePrice - calculatedDiscount).toFixed(2)}
                    </div>
                  </div>
                </div>
              </div>
            );
          })()}
          </div>

          {/* Features Count */}
          <div className="space-y-0.5">
            <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Features</div>
            <div className="flex items-center space-x-2">
              <span className="inline-flex items-center px-1 py-0 bg-blue-100 text-blue-800 text-sm font-medium rounded-md">
                {option._count?.features || 0}
              </span>
          </div>
        </div>

        {/* Additional Info */}
        <div className="flex items-center justify-between text-xs text-gray-500 pt-1 border-t border-gray-100">
          <span className="flex items-center space-x-1">
            <span className="font-medium">Orders:</span>
            <span className="bg-gray-100 px-1 py-0 rounded">{option._count?.orderDetails || 0}</span>
          </span>
          <div className="flex items-center space-x-2">
            <span className="flex items-center space-x-1">
              <span className="font-medium">Created:</span>
              <span>{new Date(option.createdAt).toLocaleDateString()}</span>
            </span>
            
            {/* Mobile Action Button - Always visible on mobile and small screens */}
            <div className="lg:hidden">
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  const actionMenu = e.currentTarget.closest('.group')?.querySelector('.action-menu') as HTMLElement;
                  if (actionMenu) {
                    const isVisible = actionMenu.style.opacity === '1';
                    actionMenu.style.opacity = isVisible ? '0' : '1';
                    actionMenu.style.transform = isVisible ? 'translateX(100%)' : 'translateX(0)';
                    actionMenu.style.pointerEvents = isVisible ? 'none' : 'auto';
                  }
                }}
                className="p-2 text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                title="Show Actions"
              >
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Actions Sidebar - Professional Overlay */}
        <div className={`action-menu absolute bg-white rounded-lg border border-gray-200 flex flex-col items-center justify-center transition-all duration-200 ${
          density === 'compact' 
            ? 'top-2 right-2 bottom-2 w-10 space-y-2' 
            : density === 'spacious' 
            ? 'top-4 right-4 bottom-4 w-14 space-y-6' 
            : 'top-3 right-3 bottom-3 w-12 space-y-4'
        }`} style={{
          opacity: '0',
          transform: 'translateX(100%)',
          pointerEvents: 'none'
        }}>
          {/* Edit Button */}
          <button
            onClick={(e) => {
              e.stopPropagation()
              handleEditOption(option)
            }}
            className={`flex items-center justify-center bg-blue-500 text-white rounded-lg border border-blue-600 hover:scale-110 shadow-sm pointer-events-auto ${
              density === 'compact' 
                ? 'w-7 h-7' 
                : density === 'spacious' 
                ? 'w-10 h-10' 
                : 'w-8 h-8'
            }`}
            title="Edit Option"
          >
            <PencilIcon className={`${
              density === 'compact' 
                ? 'h-3 w-3' 
                : density === 'spacious' 
                ? 'h-5 w-5' 
                : 'h-4 w-4'
            }`} />
          </button>
          
          {/* Toggle Active Button */}
          <button
            onClick={(e) => {
              e.stopPropagation()
              // Toggle active status functionality
            }}
            className={`flex items-center justify-center rounded-lg border hover:scale-110 shadow-sm pointer-events-auto ${
              density === 'compact' 
                ? 'w-7 h-7' 
                : density === 'spacious' 
                ? 'w-10 h-10' 
                : 'w-8 h-8'
            } ${
              option.isActive
                ? 'bg-emerald-500 border-emerald-600 text-white'
                : 'bg-gray-400 border-gray-500 text-white'
            }`}
            title={option.isActive ? 'Deactivate Option' : 'Activate Option'}
          >
            <i className={`fas fa-power-off ${
              density === 'compact' 
                ? 'text-xs' 
                : density === 'spacious' 
                ? 'text-sm' 
                : 'text-xs'
            }`} />
          </button>
          
          {/* Delete Button */}
          <button
            onClick={(e) => {
              e.stopPropagation()
              handleDeleteOption(option)
            }}
            className={`flex items-center justify-center bg-red-500 text-white rounded-lg border border-red-600 hover:scale-110 shadow-sm pointer-events-auto ${
              density === 'compact' 
                ? 'w-7 h-7' 
                : density === 'spacious' 
                ? 'w-10 h-10' 
                : 'w-8 h-8'
            }`}
            title="Delete Option"
          >
            <TrashIcon className={`${
              density === 'compact' 
                ? 'h-3 w-3' 
                : density === 'spacious' 
                ? 'h-5 w-5' 
                : 'h-4 w-4'
            }`} />
          </button>
        </div>
      </div>
    )
  }, [density, selectedOption, selectedOptions, handleOptionSelect, handleSelectOption, handleEditOption, handleDeleteOption, getOptionIcon])

  // Early return for loading state
  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Rendering
  return (
    <div className="space-y-3" data-section="options" style={{ paddingBottom: dropdownSpaceNeeded > 0 ? `${dropdownSpaceNeeded}px` : '0' }}>
      {/* Mobile Layout */}
      <div className="flex flex-col space-y-3 lg:hidden">
        {/* Search Bar - Full Width on Mobile */}
        <div className="w-full">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search options..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 text-sm"
            />
          </div>
        </div>

        {/* Mobile Controls - Single Row */}
        <div className="flex items-center gap-1">
          {/* View Mode Toggle - Stretched */}
          <div className="flex items-center bg-gray-100 rounded-lg p-0.5 flex-1">
            <button
              onClick={() => setViewMode('list')}
              className={`flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 ${
                viewMode === 'list'
                  ? 'bg-white text-amber-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              title="List view"
            >
              <ListBulletIcon className="h-3 w-3" />
              <span className="text-xs font-medium hidden xs:inline">List</span>
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 ${
                viewMode === 'grid'
                  ? 'bg-white text-amber-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              title="Grid view"
            >
              <Squares2X2Icon className="h-3 w-3" />
              <span className="text-xs font-medium hidden xs:inline">Grid</span>
            </button>
          </div>

          {/* Grid Columns Control (for grid view) */}
          {viewMode === 'grid' && (
            <div className="flex items-center bg-gray-100 rounded-lg p-0.5 flex-1">
              <span className="text-xs font-medium text-gray-700 px-1">Col:</span>
              <div className="flex items-center gap-0.5 flex-1">
                {[1, 2, 3, 4].map((num) => (
                  <button
                    key={num}
                    onClick={() => setGridColumns(num)}
                    className={`flex-1 px-1.5 py-1 rounded text-xs font-medium ${
                      gridColumns === num
                        ? 'bg-white text-green-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                    title={`${num} column${num > 1 ? 's' : ''}`}
                  >
                    {num}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Table Columns Control (for list view) */}
          {viewMode === 'list' && (
            <div className="relative flex-1">
              <button
                onClick={() => setShowColumnSelector(!showColumnSelector)}
                className="w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                title="Columns"
              >
                <ViewColumnsIcon className="h-3 w-3 mr-0.5" />
                <span className="hidden xs:inline">Col</span>
                <ChevronDownIcon className="h-3 w-3 ml-0.5" />
              </button>
            </div>
          )}

          {/* Filters Button - Stretched */}
          <div className="relative flex-1">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium rounded-lg border ${
                showFilters || Object.keys(currentFilters).some(key => currentFilters[key as keyof typeof currentFilters])
                  ? 'bg-amber-50 text-amber-700 border-amber-300'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              title="Filters"
            >
              <FunnelIcon className="h-3 w-3 mr-0.5" />
              <span className="hidden xs:inline">Filter</span>
              {Object.keys(currentFilters).some(key => currentFilters[key as keyof typeof currentFilters]) && (
                <span className="ml-0.5 inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                  {Object.values(currentFilters).filter(Boolean).length}
                </span>
              )}
            </button>
          </div>

          {/* Density Control - Stretched */}
          <div className="relative flex-1">
            <button
              onClick={() => setShowWindowList(!showWindowList)}
              className="w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
              title="Density"
            >
              <AdjustmentsHorizontalIcon className="h-3 w-3 mr-0.5" />
              <span className="hidden xs:inline">{density.charAt(0).toUpperCase() + density.slice(1)}</span>
              <ChevronDownIcon className="h-3 w-3 ml-0.5" />
            </button>
          </div>

          {/* Create Button - Stretched */}
          <button
            onClick={() => setIsFormOpen(true)}
            className="flex-1 inline-flex items-center justify-center px-3 py-1.5 bg-amber-600 text-white text-xs font-medium rounded-lg hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
          >
            <PlusIcon className="h-3 w-3 mr-0.5" />
            <span className="hidden xs:inline">Add</span>
          </button>
        </div>
      </div>

      {/* Desktop Layout - Horizontal */}
      <div className="hidden lg:flex items-center justify-between gap-4 mb-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm">
        {/* Search Bar and Filters */}
        <div className="flex items-center gap-3 flex-1 max-w-md">
          <div className="relative flex-1">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search options by name or description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 text-sm"
          />
          </div>

          {/* Filters Button - Shows active filter count */}
          <div className="relative dropdown-container">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-amber-500"
              title="Filter options"
            >
              <FunnelIcon className="h-4 w-4 mr-2" />
              Filters
              {Object.keys(currentFilters).some(key => currentFilters[key as keyof typeof currentFilters]) && (
                <span className="ml-2 inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-amber-600 rounded-full">
                  {Object.values(currentFilters).filter(Boolean).length}
                </span>
              )}
              <ChevronDownIcon className="h-4 w-4 ml-2" />
            </button>
            {showFilters && (
              <div className="hidden lg:block absolute top-full right-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                <div className="p-4">
                  <h3 className="text-sm font-medium text-gray-900 mb-3">Filter Options</h3>
                  
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select
                      value={currentFilters.status || ''}
                      onChange={(e) => setCurrentFilters(prev => ({ ...prev, status: e.target.value || undefined }))}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                    >
                      <option value="">All Statuses</option>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>
                  
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
                    <select
                      value={currentFilters.priceRange || ''}
                      onChange={(e) => setCurrentFilters(prev => ({ ...prev, priceRange: e.target.value || undefined }))}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                    >
                      <option value="">All Prices</option>
                      <option value="free">Free</option>
                      <option value="low">$1 - $100</option>
                      <option value="medium">$101 - $500</option>
                      <option value="high">$500+</option>
                    </select>
                  </div>
                  
                  <div className="flex justify-end mt-4 pt-4 border-t border-gray-200">
                    <button
                      onClick={() => {
                        setCurrentFilters({})
                        setShowFilters(false)
                      }}
                      className="px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg"
                    >
                      Clear All
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* View Options and Controls */}
        <div className="flex items-center gap-3">
          {/* View Mode Toggle - List vs Grid view */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">View:</span>
            <div className="flex items-center bg-gray-100 rounded-lg p-1 gap-1">
              <button
                onClick={() => setViewMode('list')}
                className={`px-3 py-2 rounded-md flex items-center gap-2 ${
                  viewMode === 'list'
                    ? 'bg-white text-amber-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="List view"
              >
                <ListBulletIcon className="h-5 w-5" />
                <span className="text-sm font-medium">List</span>
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`px-3 py-2 rounded-md flex items-center gap-2 ${
                  viewMode === 'grid'
                    ? 'bg-white text-amber-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Grid view"
              >
                <Squares2X2Icon className="h-5 w-5" />
                <span className="text-sm font-medium">Grid</span>
              </button>
            </div>
          </div>

          {/* Grid Columns Control (for grid view only) */}
          {viewMode === 'grid' && (
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700">Columns:</span>
              <div className="flex items-center bg-gray-100 rounded-lg p-1 gap-1">
                <button
                  onClick={() => setGridColumns(1)}
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    gridColumns === 1
                      ? 'bg-white text-amber-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="1 column"
                >
                  1
                </button>
                <button
                  onClick={() => setGridColumns(2)}
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    gridColumns === 2
                      ? 'bg-white text-amber-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="2 columns"
                >
                  2
                </button>
                <button
                  onClick={() => setGridColumns(3)}
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    gridColumns === 3
                      ? 'bg-white text-amber-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="3 columns"
                >
                  3
                </button>
                <button
                  onClick={() => setGridColumns(4)}
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    gridColumns === 4
                      ? 'bg-white text-amber-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="4 columns"
                >
                  4
                </button>
              </div>
            </div>
          )}

          {/* Column Selector (List View Only) - Toggle visible columns */}
          {viewMode === 'list' && (
            <div className="relative dropdown-container">
              <button
                onClick={() => setShowColumnSelector(!showColumnSelector)}
                className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-amber-500"
                title="Select columns to display"
              >
                <ViewColumnsIcon className="h-4 w-4 mr-2" />
                Columns
                <ChevronDownIcon className="h-4 w-4 ml-2" />
              </button>
              {showColumnSelector && (
                <div className="hidden lg:block absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                  <div className="p-2">
                    {Object.entries(visibleColumns).map(([key, visible]) => (
                      <label key={key} className="flex items-center space-x-2 py-1">
                        <input
                          type="checkbox"
                          checked={visible}
                          onChange={(e) => setVisibleColumns(prev => ({ ...prev, [key]: e.target.checked }))}
                          className="h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 rounded"
                        />
                        <span className="text-sm text-gray-700 capitalize">{key}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Density Control - Adjust table row spacing */}
          <div className="relative dropdown-container">
            <button
              onClick={() => setShowWindowList(!showWindowList)}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-amber-500"
              title="Adjust table density"
            >
              <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
              {density.charAt(0).toUpperCase() + density.slice(1)}
              <ChevronDownIcon className="h-4 w-4 ml-2" />
            </button>
            {showWindowList && (
              <div className="hidden lg:block absolute top-full left-0 mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                <div className="p-1">
                  {(['compact', 'comfortable', 'spacious'] as const).map((option) => (
                    <button
                      key={option}
                      onClick={() => {
                        handleDensityChange(option)
                        setShowWindowList(false)
                      }}
                      className={`w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100 ${
                        density === option ? 'bg-amber-100 text-amber-700' : 'text-gray-700'
                      }`}
                    >
                      {option.charAt(0).toUpperCase() + option.slice(1)}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>


          {/* Create Button - Primary action for adding new options */}
          <button
            onClick={handleCreateOption}
            className="inline-flex items-center px-4 py-2 bg-amber-600 text-white text-sm font-medium rounded-lg hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Option
          </button>
        </div>
      </div>

      {/* Mobile Dropdowns */}
      {/* Filters Dropdown - Mobile */}
      {showFilters && (
        <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-900">Filter Options</h3>
            <button
              onClick={() => setShowFilters(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <select
                value={currentFilters.status || ''}
                onChange={(e) => setCurrentFilters(prev => ({ ...prev, status: e.target.value || undefined }))}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
              <select
                value={currentFilters.priceRange || ''}
                onChange={(e) => setCurrentFilters(prev => ({ ...prev, priceRange: e.target.value || undefined }))}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Prices</option>
                <option value="free">Free</option>
                <option value="low">$1 - $100</option>
                <option value="medium">$101 - $500</option>
                <option value="high">$500+</option>
              </select>
            </div>
          </div>
          
          <div className="flex justify-end mt-4 pt-4 border-t border-gray-200">
            <button
              onClick={() => {
                setCurrentFilters({})
                setShowFilters(false)
              }}
              className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg"
            >
              Clear All
            </button>
          </div>
        </div>
      )}

      {/* Column Selector Dropdown - Mobile */}
      {showColumnSelector && (
        <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4">
          <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Show Columns</div>
          {Object.entries(visibleColumns).map(([key, visible]) => (
            <label key={key} className="flex items-center space-x-2 py-1">
              <input
                type="checkbox"
                checked={visible}
                onChange={(e) => setVisibleColumns(prev => ({ ...prev, [key]: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700 capitalize">{key}</span>
            </label>
          ))}
        </div>
      )}

      {/* Density Dropdown - Mobile */}
      {showWindowList && (
        <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-2">
          {(['compact', 'comfortable', 'spacious'] as const).map((option) => (
            <button
              key={option}
              onClick={() => {
                setDensity(option)
                setShowWindowList(false)
              }}
              className={`w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 ${
                density === option ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'
              }`}
            >
              {option.charAt(0).toUpperCase() + option.slice(1)}
            </button>
          ))}
        </div>
      )}

      {/* Options Content - Main data display area */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading options...</p>
          </div>
        ) : filteredOptions.length === 0 ? (
          <div className="p-6 text-center">
            <ListBulletIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No options found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm ? 'Try adjusting your search terms.' : 'Get started by creating your first option.'}
            </p>
            <button
              onClick={handleCreateOption}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Option
            </button>
          </div>
        ) : (
          <div>
            {viewMode === 'list' && (
              <div className="overflow-hidden">
                {/* Bulk Actions Bar - Shows when options are selected */}
                {showBulkActions && selectedOptions.length > 0 && (
                  <div className="bg-amber-50 border border-amber-200 rounded-lg px-4 py-2 shadow-sm">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center">
                            <span className="text-amber-600 font-semibold text-xs">
                              {selectedOptions.length}
                            </span>
                          </div>
                          <span className="text-xs font-medium text-amber-900">
                            {selectedOptions.length === 1 ? 'option' : 'options'} selected
                          </span>
                        </div>
                        
                        {/* Bulk action buttons for selected options */}
                        <div className="flex items-center space-x-1.5">
                          <button
                            onClick={handleBulkActivate}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 border border-green-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                            title="Activate selected options"
                          >
                            <EyeIcon className="h-3 w-3 mr-1" />
                            Activate
                          </button>
                          
                          <button
                            onClick={handleBulkDeactivate}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500"
                            title="Deactivate selected options"
                          >
                            <EyeSlashIcon className="h-3 w-3 mr-1" />
                            Deactivate
                          </button>
                          
                          <button
                            onClick={() => showBulkDeleteConfirmation(selectedOptions)}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 border border-red-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500"
                            title="Delete selected options"
                          >
                            <TrashIcon className="h-3 w-3 mr-1" />
                            Delete
                          </button>
                        </div>
                      </div>
                      
                      {/* Clear selection button */}
                      <div className="flex items-center space-x-1.5">
                        <button
                          onClick={handleClearSelection}
                          className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gray-500"
                          title="Clear selection"
                        >
                          <XMarkIcon className="h-3 w-3 mr-1" />
                          Clear
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                <div className={showBulkActions && selectedOptions.length > 0 ? "mt-3" : ""}>
                  {/* Options table with sortable columns */}
                  <table className="min-w-full">
                  <thead className="bg-gray-200 border-b border-gray-300">
                    <tr>
                      {/* Select all checkbox */}
                      <th scope="col" className="relative pl-2 py-2" style={{ width: '6px' }}>
                        <input
                          type="checkbox"
                          checked={selectedOptions.length === filteredOptions.length && filteredOptions.length > 0}
                          onChange={handleSelectAll}
                          className={`text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${
                            density === 'compact' 
                              ? 'h-4 w-4' 
                              : density === 'spacious' 
                              ? 'h-5 w-5' 
                              : 'h-4 w-4'
                          }`}
                        />
                      </th>
                      {/* Name column with sorting */}
                      {visibleColumns.name && (
                        <th scope="col" className="pl-6 pr-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('name')}>
                          <div className="flex items-center space-x-1">
                            <span>Option</span>
                            {getSortIcon('name')}
                          </div>
                        </th>
                      )}
                      {/* Description column with sorting */}
                      {visibleColumns.description && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('description')}>
                          <div className="flex items-center space-x-1">
                            <span>Description</span>
                            {getSortIcon('description')}
                          </div>
                        </th>
                      )}
                      {/* Price column with sorting */}
                      {visibleColumns.price && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('price')}>
                          <div className="flex items-center space-x-1">
                            <span>Price</span>
                            {getSortIcon('price')}
                          </div>
                        </th>
                      )}
                      {/* Discount rate column with sorting */}
                      {visibleColumns.discountRate && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('discountRate')}>
                          <div className="flex items-center space-x-1">
                            <span>Discount Rate</span>
                            {getSortIcon('discountRate')}
                          </div>
                        </th>
                      )}
                      {/* Total discount column with sorting */}
                      {visibleColumns.totalDiscount && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('totalDiscount')}>
                          <div className="flex items-center space-x-1">
                            <span>Total Discount</span>
                            {getSortIcon('totalDiscount')}
                          </div>
                        </th>
                      )}
                      {/* Features column with sorting */}
                      {visibleColumns.features && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('features')}>
                          <div className="flex items-center space-x-1">
                            <span>Features</span>
                            {getSortIcon('features')}
                          </div>
                        </th>
                      )}
                      {/* Status column with sorting */}
                      {visibleColumns.status && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('isActive')}>
                          <div className="flex items-center space-x-1">
                            <span>Status</span>
                            {getSortIcon('isActive')}
                          </div>
                        </th>
                      )}
                      {/* Actions column */}
                      {visibleColumns.actions && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider py-2 text-xs">
                          <span>Actions</span>
                        </th>
                      )}
                    </tr>
                  </thead>
                  {/* Table body with option rows */}
                  <tbody className="bg-white">
                    {paginatedOptions.map((option) => (
                      <OptionRow
                        key={option.id}
                        option={option}
                        isSelected={selectedOption?.id === option.id}
                        onOptionSelect={handleOptionSelect}
                        onEdit={handleEditOption}
                        onDelete={handleDeleteOption}
                        isChecked={selectedOptions.includes(option.id)}
                        onCheck={handleSelectOption}
                      />
                    ))}
                  </tbody>
                </table>
                </div>
              </div>
            )}

            {viewMode === 'grid' && (
              <div>
                {/* Bulk Actions Bar - Shows when options are selected */}
                {showBulkActions && selectedOptions.length > 0 && (
                  <div className="bg-amber-50 border border-amber-200 rounded-lg px-4 py-2 shadow-sm">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center">
                            <span className="text-amber-600 font-semibold text-xs">
                              {selectedOptions.length}
                            </span>
                          </div>
                          <span className="text-xs font-medium text-amber-900">
                            {selectedOptions.length === 1 ? 'option' : 'options'} selected
                          </span>
                        </div>
                        
                        {/* Bulk action buttons for selected options */}
                        <div className="flex items-center space-x-1.5">
                          <button
                            onClick={handleBulkActivate}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 border border-green-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                            title="Activate selected options"
                          >
                            <EyeIcon className="h-3 w-3 mr-1" />
                            Activate
                          </button>
                          
                          <button
                            onClick={handleBulkDeactivate}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500"
                            title="Deactivate selected options"
                          >
                            <EyeSlashIcon className="h-3 w-3 mr-1" />
                            Deactivate
                          </button>
                          
                          <button
                            onClick={() => showBulkDeleteConfirmation(selectedOptions)}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 border border-red-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500"
                            title="Delete selected options"
                          >
                            <TrashIcon className="h-3 w-3 mr-1" />
                            Delete
                          </button>
                        </div>
                      </div>
                      
                      {/* Clear selection button */}
                      <div className="flex items-center space-x-1.5">
                        <button
                          onClick={handleClearSelection}
                          className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gray-500"
                          title="Clear selection"
                        >
                          <XMarkIcon className="h-3 w-3 mr-1" />
                          Clear
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                <div className={showBulkActions && selectedOptions.length > 0 ? "mt-3" : ""}>
                  <div className={`grid gap-4 ${
                  gridColumns === 1 ? 'grid-cols-1' :
                  gridColumns === 2 ? 'grid-cols-1 md:grid-cols-2' :
                  gridColumns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
                  'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                }`}>
                    {paginatedOptions.map((option) => 
                      renderOptionCard(option, selectedOption?.id === option.id)
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>







      {/* Responsive Pagination */}
      {filteredOptions.length > 0 && (
        <ResponsivePagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          itemsPerPage={itemsPerPage}
          onItemsPerPageChange={handleItemsPerPageChange}
          totalItems={filteredOptions.length}
          startIndex={startIndex}
          endIndex={endIndex}
          itemsPerPageOptions={[5, 10, 20, 50]}
          showItemsPerPage={true}
          showPageInfo={true}
        />
      )}

      {/* Option Form Modal - For creating and editing options */}
      <OptionFormModal
        isOpen={isFormOpen}
        onClose={() => {
          setIsFormOpen(false)
          if (editingOption) {
            showInfo('Edit Cancelled', `Editing "${editingOption.name}" was cancelled.`)
          } else {
            showInfo('Create Cancelled', 'Option creation was cancelled.')
          }
        }}
        onSubmit={async (formData) => {
          try {
            const action = editingOption ? 'Updating' : 'Creating'
            showLoading(`${action} Option`, `${action.toLowerCase()} service option...`)
            
            const url = editingOption 
              ? `/api/admin/service-options/${editingOption.id}`
              : '/api/admin/service-options'
            
            const method = editingOption ? 'PUT' : 'POST'
            
            const response = await fetch(url, {
              method,
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                name: formData.name,
                description: formData.description,
                serviceid: service.id,
                price: formData.price,
                discountrate: formData.discountRate,
                totaldiscount: formData.totalDiscount || (formData.discountRate > 0 ? (formData.price * formData.discountRate) / 100 : 0),
                isactive: formData.isActive
              })
            })

            if (response.ok) {
              const data = await response.json()
              
              if (editingOption) {
                // Update existing option in both options and filteredOptions arrays
                const updatedOption = { ...editingOption, ...formData }
                setOptions(prev => prev.map(option => option.id === editingOption.id ? updatedOption : option))
                setFilteredOptions(prev => prev.map(option => option.id === editingOption.id ? updatedOption : option))
                showSuccess('Option Updated', `"${editingOption.name}" has been successfully updated.`)
              } else {
                // Create new option and add to both arrays
                const newOption = data.data
                setOptions(prev => [...prev, newOption])
                setFilteredOptions(prev => [...prev, newOption])
                showSuccess('Option Created', `"${formData.name}" has been successfully created.`)
              }
              
              setIsFormOpen(false)
              setEditingOption(null)
            } else {
              const errorData = await response.json()
              showError('Save Failed', errorData.message || 'Failed to save service option')
            }
          } catch (error) {
            console.error('Error saving option:', error)
            showError('Save Failed', 'Failed to save service option')
          }
        }}
        editingOption={editingOption}
        serviceId={service.id}
      />
      

      {/* Delete Confirmation Modal - Shows dependency information */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        title={isBulkDelete ? (bulkOptionsToDelete.length === 1 ? "Delete Option" : "Delete Multiple Options") : "Delete Option"}
        message={(() => {
          if (isBulkDelete) {
            const count = bulkOptionsToDelete.length
            if (verificationData?.canDelete) {
              return count === 1 
                ? `Are you sure you want to delete "${bulkOptionsToDelete[0].name}"?`
                : `Are you sure you want to delete ${count} options?`
            } else {
              return count === 1 
                ? `Cannot delete "${bulkOptionsToDelete[0].name}"`
                : `Cannot delete ${count} options`
            }
          }
          
          if (!verificationData) return `Are you sure you want to delete "${optionToDelete?.name}"?`
          
          if (verificationData.canDelete) {
            const hasFeatures = (verificationData.features || 0) > 0
            const hasOrders = (verificationData.orderDetails || 0) > 0
            
            // Dynamic message based on dependencies
            if (hasFeatures && hasOrders) {
              return `Delete "${optionToDelete?.name}" and all its data?`
            } else if (hasFeatures) {
              return `Delete "${optionToDelete?.name}" and its features?`
            } else if (hasOrders) {
              return `Delete "${optionToDelete?.name}" (has order history)?`
            } else {
              return `Delete "${optionToDelete?.name}"?`
            }
          } else {
            return `Cannot delete "${optionToDelete?.name}"`
          }
        })()}
        details={(() => {
          if (isBulkDelete) {
            const count = bulkOptionsToDelete.length
            if (verificationData?.canDelete) {
              return `This will permanently remove ${count} ${count === 1 ? 'option' : 'options'} and all associated data. This action cannot be undone.`
            } else {
              const totalFeatures = verificationData?.features || 0
              const totalOrderDetails = verificationData?.orderDetails || 0
              return `Some options have dependencies. Only options without features or orders will be deleted. This will skip ${totalFeatures + totalOrderDetails} dependencies.`
            }
          }
          
          if (!verificationData) return "This action cannot be undone. All associated option features will also be removed."
          
          if (verificationData.canDelete) {
            const hasFeatures = (verificationData.features || 0) > 0
            const hasOrders = (verificationData.orderDetails || 0) > 0
            
            // Dynamic details based on what will be deleted
            if (hasFeatures && hasOrders) {
              return "This will permanently remove the option, all its features, and order history. This action cannot be undone."
            } else if (hasFeatures) {
              return "This will permanently remove the option and all its features. This action cannot be undone."
            } else if (hasOrders) {
              return "This option has order history. Deleting it will remove all associated order records. This action cannot be undone."
            } else {
              return "This option has no dependencies. It will be permanently removed. This action cannot be undone."
            }
          } else {
            return "Please remove all dependencies before attempting to delete this option."
          }
        })()}
        confirmText={verificationData?.canDelete || isBulkDelete ? "Delete" : "Delete Safe Only"}
        cancelText="Cancel"
        onConfirm={confirmDeleteOption}
        onCancel={() => {
          setIsDeleteModalOpen(false)
          setOptionToDelete(null)
          setBulkOptionsToDelete([])
          setIsBulkDelete(false)
          setVerificationData(null)
          // Clear any remaining loading notifications when modal is cancelled
          clearLoadingNotifications()
        }}
        type={verificationData?.canDelete || isBulkDelete ? 'danger' : 'verification'}
        showVerification={true}
        verificationData={verificationData ? {
          canDelete: verificationData.canDelete,
          reason: verificationData.reason,
          dependencies: verificationData.dependencies
        } : undefined}
      />
    </div>
  )
}
