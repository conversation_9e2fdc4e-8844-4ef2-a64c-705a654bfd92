'use client'

// Imports
import React, { useState, useCallback, memo, useEffect } from 'react'
import '@/styles/components/cards.css'
import { AnimatePresence } from 'framer-motion'
import {
  CogIcon,
  ListBulletIcon,
  PlusIcon,
  BuildingOfficeIcon,
  Cog6ToothIcon,
  RectangleStackIcon,
  SparklesIcon,
  PencilIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  EyeSlashIcon,
  Squares2X2Icon,
  XMarkIcon,
  ChevronDownIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ViewColumnsIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline'
import { CategoryManagement } from './category-management'
import { ServiceOptionsManagement } from './service-options-management'
import { OptionFeaturesManagement } from './option-features-management'
import { useNotifications } from '@/components/providers/notification-provider'
import { ConfirmationModal } from '../shared/confirmation-modal'
import { ResponsivePagination } from '../shared/responsive-pagination'
import { ServiceFormModal } from './service-form-modal'

// Types and Interfaces
interface Category {
  id: string
  name: string
  description?: string
  parentId?: string
  isActive: boolean
  displayOrder: number
  children?: Category[]
  _count?: {
    services: number
    children: number
  }
}

interface Service {
  id: string
  categoryId: string
  name: string
  description: string
  iconClass?: string
  price: number
  discountRate?: number
  totalDiscount?: number
  manager?: string
  isActive: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
  category?: {
    id: string
    name: string
  }
  _count?: {
    serviceOptions: number
    orderDetails: number
  }
}

interface ServiceOption {
  id: string
  serviceId: string
  name: string
  description?: string
  price?: number
  discountRate?: number
  totalDiscount?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  service?: {
    id: string
    name: string
    category?: {
      id: string
      name: string
    }
  }
  _count?: {
    features: number
    orderDetails: number
  }
}

type ActiveSection = 'categories' | 'services' | 'options' | 'features'

interface ServiceManagementProps {
  category: Category
  selectedService: Service | null
  onServiceSelect: (service: Service | null) => void
  showSuccess: (title: string, message: string, details?: string) => void
  showError: (title: string, message: string, details?: string) => void
  showWarning: (title: string, message: string, details?: string) => void
  showInfo: (title: string, message: string, details?: string) => void
  showLoading: (title: string, message: string, details?: string) => string
  clearLoadingNotifications: () => void
}

interface ServiceFormData {
  name: string
  description: string
  iconClass: string
  price: number
  discountRate: number
  totalDiscount: number
  manager: string
  isActive: boolean
  displayOrder: number
}



// Memoized Components
// Section navigation component with optimized rendering
const SectionNavigation = memo<{
  sections: readonly {
    readonly id: ActiveSection
    readonly title: string
    readonly description: string
    readonly color: string
    readonly gradient: string
    readonly icon: React.ComponentType<{ className?: string }>
    readonly isActive: boolean
    readonly disabled?: boolean
    readonly selectedName?: string | null
  }[]
  onSectionChange: (sectionId: ActiveSection) => void
  getCounter: (sectionId: string) => number
}>(({ sections, onSectionChange, getCounter }) => (
  <div className="overflow-visible">
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 min-w-[640px] md:min-w-0">
      {sections.map((section) => (
        <button
          key={section.id}
          onClick={() => onSectionChange(section.id)}
          disabled={section.disabled}
          className={`group relative overflow-visible rounded-lg border text-left ${
            section.isActive
              ? section.id === 'categories' 
                ? 'bg-gradient-to-br from-blue-50 to-blue-100 border-blue-500 shadow-lg cursor-pointer'
                : section.id === 'services'
                ? 'bg-gradient-to-br from-emerald-50 to-emerald-100 border-emerald-500 shadow-lg cursor-pointer'
                : section.id === 'options'
                ? 'bg-gradient-to-br from-amber-50 to-amber-100 border-amber-500 shadow-lg cursor-pointer'
                : section.id === 'features'
                ? 'bg-gradient-to-br from-purple-50 to-purple-100 border-purple-500 shadow-lg cursor-pointer'
                : 'bg-gradient-to-br from-blue-50 to-blue-100 border-blue-500 shadow-lg cursor-pointer'
              : section.disabled
              ? 'border-gray-200 cursor-not-allowed opacity-60 bg-gray-50'
              : 'border-gray-200 hover:border-gray-300 hover:shadow-lg hover:transform hover:scale-102 bg-white hover:bg-gray-50'
          }`}
          aria-label={`Navigate to ${section.title} section`}
          aria-describedby={`${section.id}-description`}
          aria-current={section.isActive ? 'page' : undefined}
        >
          {/* Content */}
          <div className="relative px-3 py-2 rounded-lg">
            {/* Header with Icon, Title and Selected Name */}
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-xl ${
                  section.isActive
                    ? section.id === 'categories' 
                      ? 'bg-blue-100 text-blue-600'
                      : section.id === 'services'
                      ? 'bg-green-100 text-green-600'
                      : section.id === 'options'
                      ? 'bg-amber-100 text-amber-600'
                      : 'bg-purple-100 text-purple-600'
                    : section.disabled
                    ? 'bg-gray-100 text-gray-400'
                    : 'bg-gray-50 text-gray-500 group-hover:bg-gray-100'
                }`}>
                  <section.icon className="h-5 w-5" aria-hidden="true" />
                </div>
                
                {/* Counter for non-categories cards - between icon and title */}
                {section.id !== 'categories' && (
                  <div className={`text-lg sm:text-xl font-bold ${
                    section.isActive
                      ? 'text-gray-900'
                      : section.disabled
                      ? 'text-gray-400'
                      : 'text-gray-700 group-hover:text-gray-900'
                  }`}>
                    ({getCounter(section.id)})
                  </div>
                )}
                
                <h3 className={`text-lg sm:text-xl font-bold ${
                  section.isActive
                    ? 'text-gray-900'
                    : section.disabled
                    ? 'text-gray-400'
                    : 'text-gray-700 group-hover:text-gray-900'
                }`}>
                  {String(section.title)}
                </h3>
              </div>
            </div>

            {/* Description */}
            <div className="mb-1">
              <p
                id={`${section.id}-description`}
                className={`text-xs sm:text-sm leading-relaxed ${
                  section.isActive
                    ? 'text-gray-600'
                    : section.disabled
                    ? 'text-gray-400'
                    : 'text-gray-500 group-hover:text-gray-600'
                }`}
              >
                {String(section.description)}
              </p>
            </div>
            
            {/* Bottom Section: Selected Name (Left) and Row Counter (Right) */}
            <div className="mt-auto flex items-center gap-2 min-w-0">
              {/* Selected Name - Left Side */}
              {section.selectedName && (
                <div className="flex-1 min-w-0">
                  <div className={`inline-flex items-center px-2 sm:px-3 py-1.5 rounded-md max-w-full ${
                    section.isActive
                      ? section.id === 'categories' 
                        ? 'bg-blue-100/80 text-blue-900'
                        : section.id === 'services'
                        ? 'bg-green-100/80 text-green-900'
                        : section.id === 'options'
                        ? 'bg-amber-100/80 text-amber-900'
                        : 'bg-purple-100/80 text-purple-900'
                      : section.id === 'categories' 
                        ? 'bg-blue-50/80 text-blue-800'
                        : section.id === 'services'
                        ? 'bg-emerald-50/80 text-emerald-800'
                        : section.id === 'options'
                        ? 'bg-amber-50/80 text-amber-800'
                        : 'bg-purple-50/80 text-purple-800'
                  }`}>
                    <div className="flex items-center space-x-1.5 sm:space-x-2 min-w-0">
                      <div className={`w-1.5 h-1.5 rounded-full flex-shrink-0 ${
                        section.id === 'categories' 
                          ? 'bg-blue-600'
                          : section.id === 'services'
                          ? 'bg-emerald-600'
                          : section.id === 'options'
                          ? 'bg-amber-600'
                          : 'bg-purple-600'
                      }`}></div>
                      <div className="text-xs font-medium truncate min-w-0" title={section.selectedName}>
                        {section.selectedName}
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              {/* Row Counter - Right Side (only for categories card) */}
              {section.id === 'categories' && (
                <div className="text-right flex-shrink-0">
                  <div className={`text-xs font-semibold whitespace-nowrap ${
                    section.isActive
                      ? section.id === 'categories' 
                      ? 'text-blue-600'
                        : 'text-green-600'
                      : section.disabled
                      ? 'text-gray-400'
                      : 'text-gray-500'
                  }`}>
                    {`${getCounter(section.id)} Categories`}
                  </div>
                </div>
              )}
            </div>
            
          </div>

          {/* CSS Triangle Arrow - show for all cards except features */}
          {section.id !== 'features' && (
            <div className={`absolute top-1/2 right-[-20px] transform -translate-y-1/2 w-0 h-0 
                            border-t-[20px] border-t-transparent 
                            border-b-[20px] border-b-transparent 
                            border-l-[20px] ${
                              section.isActive
                                ? section.id === 'categories' 
                                  ? 'border-l-blue-50'
                                  : section.id === 'services'
                                  ? 'border-l-emerald-50'
                                  : section.id === 'options'
                                  ? 'border-l-amber-50'
                                  : 'border-l-blue-50'
                                : 'border-l-white'
                            }`}
                 style={{
                   filter: 'drop-shadow(1px 0 0 #d1d5db)'
                 }}>
            </div>
          )}
        </button>
      ))}
    </div>
  </div>
))
SectionNavigation.displayName = 'SectionNavigation'

// Header component with memoization for performance
const Header = memo<{
  selectedCategory: Category | null
  selectedService: Service | null
  selectedOption: ServiceOption | null
}>(({ selectedCategory, selectedService, selectedOption }) => (
  <div className="relative rounded-lg shadow-sm border border-gray-200/50 overflow-hidden">
    {/* Background Pattern */}
    <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/20" />

    <div className="relative p-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <CogIcon className="h-14 w-14 text-emerald-600 -mt-2" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mt-2">
              Services Management
            </h1>
            <p className="text-sm font-medium text-gray-600">
              Manage your service hierarchy
            </p>
          </div>
        </div>

        <div className="hidden lg:flex items-center space-x-2">
          <div className="h-2 w-2 bg-green-500 rounded-full" />
          <span className="text-xs font-semibold text-gray-700 uppercase tracking-wide">Active</span>
        </div>
      </div>

    </div>
  </div>
))
Header.displayName = 'Header'

// Main Service Management Component
// Handles service CRUD operations, filtering, sorting, and bulk actions
function ServiceManagementComponent({ 
  category, 
  selectedService, 
  onServiceSelect, 
  showSuccess, 
  showError, 
  showWarning, 
  showInfo, 
  showLoading, 
  clearLoadingNotifications 
}: ServiceManagementProps) {
  // State Management
  // Core data state
  const [services, setServices] = useState<Service[]>([])
  const [filteredServices, setFilteredServices] = useState<Service[]>([])
  const [loading, setLoading] = useState(true)
  
  // Form and editing state
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingService, setEditingService] = useState<Service | null>(null)
  const [formData, setFormData] = useState<ServiceFormData>({
    name: '',
    description: '',
    iconClass: '',
    price: 0,
    discountRate: 0,
    totalDiscount: 0,
    manager: '',
    isActive: true,
    displayOrder: 0
  })
  
  // Selection and bulk actions state
  const [selectedServices, setSelectedServices] = useState<string[]>([])
  const [showBulkActions, setShowBulkActions] = useState(false)
  
  // Search and filtering state
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [currentFilters, setCurrentFilters] = useState<{
    status?: string
    priceRange?: string
    [key: string]: string | undefined
  }>({})
  
  // View and display state
  const [viewMode, setViewMode] = useState<'table' | 'list' | 'grid' | 'card'>('grid')
  const [density, setDensity] = useState<'compact' | 'comfortable' | 'spacious'>('compact')
  const [gridColumns, setGridColumns] = useState<number>(3)
  const [visibleColumns, setVisibleColumns] = useState<{
    name: boolean
    description: boolean
    price: boolean
    discountRate: boolean
    totalDiscount: boolean
    manager: boolean
    options: boolean
    status: boolean
    actions: boolean
  }>({
    name: true,
    description: true,
    price: true,
    discountRate: true,
    totalDiscount: true,
    manager: true,
    options: true,
    status: true,
    actions: true
  })
  
  // UI state for dropdowns and modals
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnSelector, setShowColumnSelector] = useState(false)
  const [showWindowList, setShowWindowList] = useState(false)
  const [dropdownSpaceNeeded, setDropdownSpaceNeeded] = useState(0)
  
  // Sorting state
  const [sortField, setSortField] = useState<string>('')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  
  // Confirmation modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [serviceToDelete, setServiceToDelete] = useState<Service | null>(null)
  const [isBulkDelete, setIsBulkDelete] = useState(false)
  const [bulkServicesToDelete, setBulkServicesToDelete] = useState<Service[]>([])
  const [verificationData, setVerificationData] = useState<{
    canDelete: boolean
    reason?: string
    serviceOptions?: number
    orderDetails?: number
    dependencies?: string[]
  } | null>(null)
  const [isVerifying, setIsVerifying] = useState(false)
  
  // Helper Functions and Utilities
  // Safe form data with fallback values to prevent undefined errors
  const safeFormData = {
    name: formData.name || '',
    description: formData.description || '',
    iconClass: formData.iconClass || '',
    price: formData.price || 0,
    discountRate: formData.discountRate || 0,
    totalDiscount: formData.totalDiscount || 0,
    manager: formData.manager || '',
    isActive: formData.isActive ?? true,
    displayOrder: formData.displayOrder || 0
  }

  // Reset form to initial state - memoized to prevent unnecessary re-renders
  const resetForm = useCallback(() => {
    setFormData({
      name: '',
      description: '',
      iconClass: '',
      price: 0,
      discountRate: 0,
      totalDiscount: 0,
      manager: '',
      isActive: true,
      displayOrder: 0
    })
    setEditingService(null)
  }, [])

  // Get default column visibility based on density setting
  const getDefaultColumns = useCallback((density: 'compact' | 'comfortable' | 'spacious') => {
    return {
      name: true,
      description: true,
      price: true,
      discountRate: true,
      totalDiscount: true,
      manager: true,
      options: true,
      status: true,
      actions: true
    }
  }, [])

  // Handle density change with column visibility reset
  const handleDensityChange = useCallback((newDensity: 'compact' | 'comfortable' | 'spacious') => {
    setDensity(newDensity)
    setVisibleColumns(getDefaultColumns(newDensity))
  }, [getDefaultColumns])

  // Service Icon Mapping - Maps service names to appropriate FontAwesome icons
  // This provides visual context and improves UX by showing relevant icons
  const getServiceIcon = useCallback((service: Service): string => {
    const name = service.name.toLowerCase()

    // Web Development Services
    if (name.includes('website') || name.includes('web development')) {
      return 'fa-code text-blue-500'
    }
    if (name.includes('web design') || name.includes('frontend')) {
      return 'fa-paint-brush text-purple-500'
    }
    if (name.includes('backend') || name.includes('api')) {
      return 'fa-database text-gray-600'
    }

    // E-commerce Services
    if (name.includes('ecommerce') || name.includes('online store') || name.includes('shop')) {
      return 'fa-store text-orange-500'
    }
    if (name.includes('payment') || name.includes('checkout')) {
      return 'fa-credit-card text-green-600'
    }

    // Mobile Development
    if (name.includes('mobile app') || name.includes('ios') || name.includes('android')) {
      return 'fa-mobile-alt text-green-500'
    }
    if (name.includes('react native') || name.includes('flutter')) {
      return 'fa-mobile text-blue-400'
    }

    // Design Services
    if (name.includes('logo') || name.includes('branding')) {
      return 'fa-copyright text-pink-500'
    }
    if (name.includes('ui design') || name.includes('interface')) {
      return 'fa-desktop text-purple-400'
    }
    if (name.includes('graphic design') || name.includes('print')) {
      return 'fa-image text-red-400'
    }

    // Marketing & SEO
    if (name.includes('seo') || name.includes('search engine')) {
      return 'fa-search text-green-600'
    }
    if (name.includes('social media') || name.includes('facebook') || name.includes('instagram')) {
      return 'fa-share-alt text-green-600'
    }
    if (name.includes('email marketing') || name.includes('newsletter')) {
      return 'fa-envelope text-red-500'
    }
    if (name.includes('advertising') || name.includes('ads')) {
      return 'fa-bullhorn text-orange-600'
    }

    // Technical Services
    if (name.includes('hosting') || name.includes('server')) {
      return 'fa-server text-gray-500'
    }
    if (name.includes('domain') || name.includes('dns')) {
      return 'fa-globe text-blue-300'
    }
    if (name.includes('ssl') || name.includes('certificate')) {
      return 'fa-lock text-green-700'
    }
    if (name.includes('backup') || name.includes('restore')) {
      return 'fa-cloud text-gray-400'
    }

    // Maintenance & Support
    if (name.includes('maintenance') || name.includes('update')) {
      return 'fa-wrench text-yellow-500'
    }
    if (name.includes('support') || name.includes('help')) {
      return 'fa-headset text-teal-500'
    }
    if (name.includes('monitoring') || name.includes('uptime')) {
      return 'fa-heartbeat text-red-600'
    }

    // Analytics & Reporting
    if (name.includes('analytics') || name.includes('google analytics')) {
      return 'fa-chart-line text-teal-600'
    }
    if (name.includes('report') || name.includes('dashboard')) {
      return 'fa-chart-bar text-indigo-500'
    }

    // Consulting & Strategy
    if (name.includes('consulting') || name.includes('consultation')) {
      return 'fa-user-tie text-indigo-600'
    }
    if (name.includes('strategy') || name.includes('planning')) {
      return 'fa-chess text-purple-600'
    }
    if (name.includes('audit') || name.includes('review')) {
      return 'fa-clipboard-check text-orange-400'
    }

    // Content Services
    if (name.includes('content') || name.includes('copywriting')) {
      return 'fa-pen text-blue-700'
    }
    if (name.includes('blog') || name.includes('article')) {
      return 'fa-newspaper text-gray-700'
    }

    // Security Services
    if (name.includes('security') || name.includes('penetration')) {
      return 'fa-shield-alt text-red-500'
    }
    if (name.includes('firewall') || name.includes('protection')) {
      return 'fa-shield text-red-700'
    }

    // Default fallback for unrecognized services
    return 'fa-cog text-gray-500'
  }, [])

  // Selection Handlers
  // Toggle individual service selection for bulk operations
  const handleSelectService = useCallback((serviceId: string) => {
    setSelectedServices(prev => {
      const newSelection = prev.includes(serviceId) 
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
      setShowBulkActions(newSelection.length > 0)
      return newSelection
    })
  }, [])

  // Toggle select all services for bulk operations
  const handleSelectAll = useCallback(() => {
    if (selectedServices.length === filteredServices.length) {
      setSelectedServices([])
      setShowBulkActions(false)
    } else {
      setSelectedServices(filteredServices.map(service => service.id))
      setShowBulkActions(true)
    }
  }, [selectedServices.length, filteredServices])

  // Bulk Action Handlers
  // Activate multiple services simultaneously
  const handleBulkActivate = useCallback(async () => {
    if (selectedServices.length === 0) return
    
    try {
      showLoading('Activating Services', `Activating ${selectedServices.length} service${selectedServices.length === 1 ? '' : 's'}...`)
      
      const promises = selectedServices.map(serviceId => {
        const service = services.find(s => s.id === serviceId)
        if (!service) return Promise.resolve()
        
        return fetch(`/api/admin/services/${serviceId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            servicename: service.name,
            servicedesc: service.description,
            categoryid: service.categoryId,
            price: service.price,
            discountrate: service.discountRate,
            totaldiscount: service.totalDiscount,
            manager: service.manager,
            isactive: true,
            displayorder: service.displayOrder,
            iconclass: service.iconClass
          })
        })
      })
      
      await Promise.all(promises)
      
      // Update local state optimistically
      setServices(prev => prev.map(service => 
        selectedServices.includes(service.id) 
          ? { ...service, isActive: true }
          : service
      ))
      setFilteredServices(prev => prev.map(service => 
        selectedServices.includes(service.id) 
          ? { ...service, isActive: true }
          : service
      ))
      
      showSuccess('Services Activated', `Successfully activated ${selectedServices.length} service${selectedServices.length === 1 ? '' : 's'}.`)
      setSelectedServices([])
      setShowBulkActions(false)
    } catch (error) {
      console.error('Error activating services:', error)
      showError('Activation Failed', 'Failed to activate selected services')
    }
  }, [selectedServices, services, showLoading, showSuccess, showError])

  // Deactivate multiple services simultaneously
  const handleBulkDeactivate = useCallback(async () => {
    if (selectedServices.length === 0) return
    
    try {
      showLoading('Deactivating Services', `Deactivating ${selectedServices.length} service${selectedServices.length === 1 ? '' : 's'}...`)
      
      const promises = selectedServices.map(serviceId => {
        const service = services.find(s => s.id === serviceId)
        if (!service) return Promise.resolve()
        
        return fetch(`/api/admin/services/${serviceId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            servicename: service.name,
            servicedesc: service.description,
            categoryid: service.categoryId,
            price: service.price,
            discountrate: service.discountRate,
            totaldiscount: service.totalDiscount,
            manager: service.manager,
            isactive: false,
            displayorder: service.displayOrder,
            iconclass: service.iconClass
          })
        })
      })
      
      await Promise.all(promises)
      
      // Update local state optimistically
      setServices(prev => prev.map(service => 
        selectedServices.includes(service.id) 
          ? { ...service, isActive: false }
          : service
      ))
      setFilteredServices(prev => prev.map(service => 
        selectedServices.includes(service.id) 
          ? { ...service, isActive: false }
          : service
      ))
      
      showSuccess('Services Deactivated', `Successfully deactivated ${selectedServices.length} service${selectedServices.length === 1 ? '' : 's'}.`)
      setSelectedServices([])
      setShowBulkActions(false)
    } catch (error) {
      console.error('Error deactivating services:', error)
      showError('Deactivation Failed', 'Failed to deactivate selected services')
    }
  }, [selectedServices, services, showLoading, showSuccess, showError])

  // Show confirmation modal for bulk delete with dependency verification
  const showBulkDeleteConfirmation = useCallback(async (serviceIds: string[]) => {
    const count = serviceIds.length
    const servicesToDelete = services.filter(service => serviceIds.includes(service.id))
    
    try {
      showLoading('Verifying Services', `Checking dependencies for ${servicesToDelete.length} services...`)
      
      // Check dependencies for each service
      const verificationPromises = servicesToDelete.map(async (service) => {
        try {
          const response = await fetch(`/api/admin/services/${service.id}`)
          if (response.ok) {
            const serviceData = await response.json()
            const freshService = serviceData.data
            
            const serviceOptionsCount = freshService._count?.serviceOptions || 0
            const orderDetailsCount = freshService._count?.orderDetails || 0
            
            return {
              service,
              canDelete: serviceOptionsCount === 0 && orderDetailsCount === 0,
              serviceOptions: serviceOptionsCount,
              orderDetails: orderDetailsCount,
              dependencies: [
                ...(serviceOptionsCount > 0 ? [`${serviceOptionsCount} service option${serviceOptionsCount === 1 ? '' : 's'}`] : []),
                ...(orderDetailsCount > 0 ? [`${orderDetailsCount} order${orderDetailsCount === 1 ? '' : 's'}`] : [])
              ]
            }
          }
          return {
            service,
            canDelete: false,
            serviceOptions: 0,
            orderDetails: 0,
            dependencies: ['Unable to verify dependencies']
          }
        } catch (error) {
          return {
            service,
            canDelete: false,
            serviceOptions: 0,
            orderDetails: 0,
            dependencies: ['Verification failed']
          }
        }
      })
      
      const verificationResults = await Promise.all(verificationPromises)
      
      // Calculate overall verification data
      const totalServiceOptions = verificationResults.reduce((sum, result) => sum + result.serviceOptions, 0)
      const totalOrderDetails = verificationResults.reduce((sum, result) => sum + result.orderDetails, 0)
      const canDeleteAll = verificationResults.every(result => result.canDelete)
      const safeToDelete = verificationResults.filter(result => result.canDelete)
      const unsafeToDelete = verificationResults.filter(result => !result.canDelete)
      
      let reason = ''
      let dependencies: string[] = []
      
      if (canDeleteAll) {
        reason = 'All selected services are safe to delete'
        dependencies = []
      } else {
        reason = `${unsafeToDelete.length} of ${servicesToDelete.length} services have dependencies`
        dependencies = [
          `${totalServiceOptions} total service option${totalServiceOptions === 1 ? '' : 's'}`,
          `${totalOrderDetails} total order${totalOrderDetails === 1 ? '' : 's'}`
        ]
      }
      
      setBulkServicesToDelete(servicesToDelete)
      setIsBulkDelete(true)
      setVerificationData({
        canDelete: canDeleteAll,
        reason,
        serviceOptions: totalServiceOptions,
        orderDetails: totalOrderDetails,
        dependencies
      })
      setIsDeleteModalOpen(true)
      
    } catch (error) {
      console.error('Error verifying bulk delete:', error)
      showError('Verification Error', 'Failed to verify service dependencies.')
    }
  }, [selectedServices, services, showLoading, showError])

  // Clear all selected services
  const handleClearSelection = useCallback(() => {
    setSelectedServices([])
    setShowBulkActions(false)
  }, [])

  // Sorting Handlers
  // Handle column sorting with toggle functionality
  const handleSort = useCallback((field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }, [sortField, sortDirection])

  // Get appropriate sort icon based on current sort state
  const getSortIcon = useCallback((field: string) => {
    if (sortField !== field) {
      return <ArrowUpIcon className="h-3 w-3 text-gray-300" />
    }
    return sortDirection === 'asc' 
      ? <ArrowUpIcon className="h-3 w-3 text-green-600" />
      : <ArrowDownIcon className="h-3 w-3 text-green-600" />
  }, [sortField, sortDirection])

  // ServiceRow Component - Memoized for performance
  // Renders individual service rows in table view with all necessary actions
  const ServiceRow = memo(({ 
    service, 
    isSelected, 
    onServiceSelect, 
    onEdit, 
    onDelete,
    isChecked,
    onCheck
  }: {
    service: Service
    isSelected: boolean
    onServiceSelect: (service: Service) => void
    onEdit: (service: Service) => void
    onDelete: (service: Service) => void
    isChecked: boolean
    onCheck: (serviceId: string) => void
  }) => {
    const isCurrentlySelected = isSelected

    return (
      <tr
        className={`group hover:bg-gray-50 cursor-pointer ${
          isCurrentlySelected ? 'bg-blue-50' : ''
        } ${
          isChecked ? 'bg-green-50 border-l-4 border-green-500' : ''
        } ${
          density === 'compact' 
            ? 'py-0' 
            : density === 'spacious' 
            ? 'py-4' 
            : 'py-2'
        }`}
        onClick={() => onServiceSelect(service)}
      >
        <td className={`pl-2 whitespace-nowrap ${
          density === 'compact' 
            ? 'py-0' 
            : density === 'spacious' 
            ? 'py-4' 
            : 'py-2'
        }`} style={{ width: '6px' }}>
          <input
            type="checkbox"
            checked={isChecked}
            onChange={() => onCheck(service.id)}
            onClick={(e) => e.stopPropagation()}
            className={`text-green-600 focus:ring-blue-500 border-gray-300 rounded ${
              density === 'compact' 
                ? 'h-4 w-4' 
                : density === 'spacious' 
                ? 'h-5 w-5' 
                : 'h-4 w-4'
            }`}
          />
        </td>
        {visibleColumns.name && (
          <td className={`pl-4 pr-6 whitespace-nowrap w-24 ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <div className="flex items-center">
              {/* Service Icon */}
              <div className={`flex items-center justify-center mr-1 ${
                density === 'compact' 
                  ? 'w-8 h-8' 
                  : density === 'spacious' 
                  ? 'w-10 h-10' 
                  : 'w-8 h-8'
              }`}>
                <i className={`fas ${getServiceIcon(service)} ${
                  density === 'compact' 
                    ? 'text-lg' 
                    : density === 'spacious' 
                    ? 'text-xl' 
                    : 'text-lg'
                }`}></i>
              </div>

              {/* Service Name */}
              <div className="flex-1 min-w-0">
                <span className={`text-gray-500 truncate font-bold ${
                  density === 'compact' 
                    ? 'text-sm' 
                    : density === 'spacious' 
                    ? 'text-base' 
                    : 'text-sm'
                }`}>
                  {service.name}
                </span>
              </div>
            </div>
          </td>
        )}

        {visibleColumns.description && (
          <td className={`px-6 whitespace-nowrap w-80 ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`text-gray-500 truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            }`}>
              {service.description || 'No description'}
            </span>
          </td>
        )}

        {visibleColumns.price && (
          <td className={`px-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`text-gray-500 truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            }`}>
              ${service.price.toLocaleString()}
            </span>
          </td>
        )}

        {visibleColumns.discountRate && (
          <td className={`px-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`text-gray-500 truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            }`}>
              {service.discountRate || 0}%
            </span>
          </td>
        )}

        {visibleColumns.totalDiscount && (
          <td className={`px-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            } ${
              (() => {
                const basePrice = service.price || 0;
                const discountRate = service.discountRate || 0;
                const calculatedDiscount = basePrice * (discountRate / 100);
                return calculatedDiscount > 0 ? 'text-red-600 font-medium' : 'text-gray-500';
              })()
            }`}>
              {(() => {
                const basePrice = service.price || 0;
                const discountRate = service.discountRate || 0;
                const calculatedDiscount = basePrice * (discountRate / 100);
                return calculatedDiscount > 0 ? `-$${calculatedDiscount.toFixed(2)}` : '$0';
              })()}
            </span>
          </td>
        )}

        {visibleColumns.manager && (
          <td className={`px-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`text-gray-500 truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            }`}>
              {service.manager || 'No manager'}
            </span>
          </td>
        )}

        {visibleColumns.options && (
          <td className={`px-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`text-gray-500 truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            }`}>
              {service._count?.serviceOptions || 0}
            </span>
          </td>
        )}


        {visibleColumns.status && (
          <td className={`px-6 whitespace-nowrap ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
            <span className={`text-gray-500 truncate ${
              density === 'compact' 
                ? 'text-sm' 
                : density === 'spacious' 
                ? 'text-base' 
                : 'text-sm'
            }`}>
              {service.isActive ? 'Active' : 'Inactive'}
            </span>
          </td>
        )}

        {visibleColumns.actions && (
          <td className={`px-6 whitespace-nowrap text-right text-sm font-medium ${
            density === 'compact' 
              ? 'py-0' 
              : density === 'spacious' 
              ? 'py-4' 
              : 'py-2'
          }`}>
             <div className="flex items-center justify-start space-x-2">
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onEdit(service)
                }}
                className="text-green-600 hover:text-green-900 p-1 hover:scale-110"
                title="Edit service"
              >
                <PencilIcon className={`${
                  density === 'compact' 
                    ? 'h-4 w-4' 
                    : density === 'spacious' 
                    ? 'h-5 w-5' 
                    : 'h-4 w-4'
                }`} />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete(service)
                }}
                className="text-red-600 hover:text-red-900 p-1 hover:scale-110"
                title="Delete service"
              >
                <TrashIcon className={`${
                  density === 'compact' 
                    ? 'h-4 w-4' 
                    : density === 'spacious' 
                    ? 'h-5 w-5' 
                    : 'h-4 w-4'
                }`} />
              </button>
            </div>
          </td>
        )}
      </tr>
    )
  })
  
  ServiceRow.displayName = 'ServiceRow'

  // Effects and Side Effects
  // Debounce search term to prevent excessive filtering
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchTerm])

  // Fetch services when category changes
  useEffect(() => {
    fetchServices()
  }, [category.id])

  // Handle click outside to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('.dropdown-container')) {
        setShowFilters(false)
        setShowColumnSelector(false)
        setShowWindowList(false)
      }
    }

    if (showFilters || showColumnSelector || showWindowList) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showFilters, showColumnSelector, showWindowList])

  // Filter services when dependencies change
  useEffect(() => {
    filterServices()
  }, [services, debouncedSearchTerm, currentFilters, sortField, sortDirection])

  // Filtering and Sorting Logic
  // Apply search, filters, and sorting to services list
  const filterServices = useCallback(() => {
    let filtered = [...services]

    // Apply search filter - searches name, description, and manager
    if (debouncedSearchTerm.trim()) {
      const searchLower = debouncedSearchTerm.toLowerCase()
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(searchLower) ||
        (service.description && service.description.toLowerCase().includes(searchLower)) ||
        (service.manager && service.manager.toLowerCase().includes(searchLower))
      )
    }

    // Apply status filter
    if (currentFilters.status) {
      filtered = filtered.filter(service => {
        if (currentFilters.status === 'active') return service.isActive
        if (currentFilters.status === 'inactive') return !service.isActive
        return true
      })
    }

    // Apply price range filter
    if (currentFilters.priceRange) {
      filtered = filtered.filter(service => {
        const price = service.price || 0
        if (currentFilters.priceRange === 'free') return price === 0
        if (currentFilters.priceRange === 'low') return price > 0 && price <= 100
        if (currentFilters.priceRange === 'medium') return price > 100 && price <= 500
        if (currentFilters.priceRange === 'high') return price > 500
        return true
      })
    }

    // Apply sorting with proper type handling
    if (sortField) {
      filtered.sort((a, b) => {
        let aValue: any = a[sortField as keyof Service]
        let bValue: any = b[sortField as keyof Service]

        // Handle different field types for proper sorting
        if (sortField === 'name' || sortField === 'description' || sortField === 'manager') {
          aValue = (aValue || '').toLowerCase()
          bValue = (bValue || '').toLowerCase()
        } else if (sortField === 'isActive') {
          aValue = aValue ? 1 : 0
          bValue = bValue ? 1 : 0
        } else if (sortField === 'price' || sortField === 'discountRate' || sortField === 'totalDiscount' || sortField === 'displayOrder') {
          aValue = aValue || 0
          bValue = bValue || 0
        } else if (sortField === 'options') {
          aValue = a._count?.serviceOptions || 0
          bValue = b._count?.serviceOptions || 0
        } else if (sortField === 'createdAt' || sortField === 'updatedAt') {
          aValue = new Date(aValue).getTime()
          bValue = new Date(bValue).getTime()
        }

        // Apply sort direction
        if (sortDirection === 'asc') {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
        } else {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
        }
      })
    }

    setFilteredServices(filtered)
    setCurrentPage(1) // Reset to first page when filtering or sorting
  }, [services, debouncedSearchTerm, currentFilters, sortField, sortDirection])
  
  // Pagination Logic
  // Calculate pagination values
  const totalPages = Math.ceil(filteredServices.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedServices = filteredServices.slice(startIndex, endIndex)
  
  // Pagination handlers
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page)
  }, [])
  
  const handleItemsPerPageChange = useCallback((items: number) => {
    setItemsPerPage(items)
    setCurrentPage(1) // Reset to first page when changing items per page
  }, [])
  
  // Calculate dropdown space needed for proper layout
  useEffect(() => {
    let spaceNeeded = 0
    
    if (showColumnSelector) {
      spaceNeeded = Math.max(spaceNeeded, 200) // Column selector dropdown height
    }
    
    if (showWindowList) {
      spaceNeeded = Math.max(spaceNeeded, 120) // Density dropdown height
    }
    
    if (showFilters) {
      spaceNeeded = Math.max(spaceNeeded, 300) // Filters dropdown height
    }
    
    setDropdownSpaceNeeded(spaceNeeded)
  }, [showColumnSelector, showWindowList, showFilters])

  // Data Fetching Functions
  // Fetch services for the selected category with error handling
  const fetchServices = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/services?categoryId=${category.id}&limit=100`)
      
      if (response.ok) {
        const data = await response.json()
        const servicesData = data.data || data.services || []
        setServices(servicesData)
        setFilteredServices(servicesData)
        
        if (servicesData.length > 0) {
          showSuccess('Services Loaded', `Loaded ${servicesData.length} service${servicesData.length === 1 ? '' : 's'} for "${category.name}"`)
        } else {
          showInfo('No Services Found', `No services found for "${category.name}". Create the first one!`)
        }
      } else {
        console.error('Failed to fetch services:', response.status, response.statusText)
        setServices([])
        setFilteredServices([])
      }
    } catch (error) {
      console.error('Error fetching services:', error)
      setServices([])
      setFilteredServices([])
    } finally {
      setLoading(false)
    }
  }, [category.id, category.name, showSuccess, showInfo])

  // Service Action Handlers
  // Handle service selection for navigation
  const handleServiceSelect = useCallback((service: Service) => {
    onServiceSelect(service)
  }, [onServiceSelect])

  // Handle create service action
  const handleCreateService = useCallback(() => {
    setIsFormOpen(true)
    resetForm()
    showInfo('Create Service', 'Fill out the form below to create a new service.')
  }, [resetForm, showInfo])

  // Handle edit service action
  const handleEditService = useCallback((service: Service) => {
    setEditingService(service)
    setFormData({
      name: service.name,
      description: service.description,
      iconClass: service.iconClass || '',
      price: service.price,
      discountRate: service.discountRate || 0,
      totalDiscount: service.totalDiscount || 0,
      manager: service.manager || '',
      isActive: service.isActive,
      displayOrder: service.displayOrder
    })
    setIsFormOpen(true)
    showInfo('Edit Service', `Editing service: "${service.name}"`)
  }, [showInfo])

  // Delete Service Functions
  // Check service dependencies before deletion
  const checkServiceDependencies = useCallback(async (service: Service) => {
    setIsVerifying(true)
    try {
      // Fetch fresh data from API to get current dependency counts
      const response = await fetch(`/api/admin/services/${service.id}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch service details')
      }
      
      const data = await response.json()
      const freshService = data.data
      
      // Check for service options and order details using fresh data
      const serviceOptionsCount = freshService._count?.serviceOptions || 0
      const orderDetailsCount = freshService._count?.orderDetails || 0
      
      const dependencies: string[] = []
      let canDelete = true
      let reason = ''
      
      if (serviceOptionsCount > 0) {
        dependencies.push(`${serviceOptionsCount} service option${serviceOptionsCount === 1 ? '' : 's'}`)
        canDelete = false
        reason = 'Service has associated options that must be removed first'
      }
      
      if (orderDetailsCount > 0) {
        dependencies.push(`${orderDetailsCount} order${orderDetailsCount === 1 ? '' : 's'}`)
        canDelete = false
        reason = 'Service is associated with existing orders'
      }
      
      if (canDelete) {
        reason = 'No dependencies found - safe to delete'
      }
      
      setVerificationData({
        canDelete,
        reason,
        serviceOptions: serviceOptionsCount,
        orderDetails: orderDetailsCount,
        dependencies
      })
      
    } catch (error) {
      console.error('Error checking service dependencies:', error)
      setVerificationData({
        canDelete: false,
        reason: 'Error checking dependencies',
        dependencies: ['Unable to verify dependencies']
      })
    } finally {
      setIsVerifying(false)
    }
  }, [])

  // Handle delete service action with dependency checking
  const handleDeleteService = useCallback(async (service: Service) => {
    setServiceToDelete(service)
    showLoading('Verifying Service', `Checking if "${service.name}" can be deleted...`)
    setIsVerifying(true)
    await checkServiceDependencies(service)
    setIsDeleteModalOpen(true)
  }, [checkServiceDependencies, showLoading])

  // Execute bulk delete after confirmation
  const executeBulkDelete = useCallback(async () => {
    if (!bulkServicesToDelete.length) return

    try {
      // If not all services can be deleted, only delete the safe ones
      if (!verificationData?.canDelete) {
        showLoading('Deleting Safe Services', 'Deleting only services without dependencies...')
        
        // Re-verify each service to get fresh data
        const verificationPromises = bulkServicesToDelete.map(async (service) => {
          try {
            const response = await fetch(`/api/admin/services/${service.id}`)
            if (response.ok) {
              const serviceData = await response.json()
              const freshService = serviceData.data
              const serviceOptionsCount = freshService._count?.serviceOptions || 0
              const orderDetailsCount = freshService._count?.orderDetails || 0
              return {
                service,
                canDelete: serviceOptionsCount === 0 && orderDetailsCount === 0
              }
            }
            return { service, canDelete: false }
          } catch (error) {
            return { service, canDelete: false }
          }
        })
        
        const verificationResults = await Promise.all(verificationPromises)
        const safeToDelete = verificationResults.filter(result => result.canDelete)
        const unsafeToDelete = verificationResults.filter(result => !result.canDelete)
        
        if (safeToDelete.length === 0) {
          showWarning('No Safe Services', 'None of the selected services can be deleted due to dependencies.')
          setIsDeleteModalOpen(false)
          setBulkServicesToDelete([])
          setIsBulkDelete(false)
          setVerificationData(null)
          return
        }
        
        // Delete only safe services
        const deletePromises = safeToDelete.map(result => 
          fetch(`/api/admin/services/${result.service.id}`, {
            method: 'DELETE',
          })
        )
        
        await Promise.all(deletePromises)
        
        let message = `Successfully deleted ${safeToDelete.length} services.`
        if (unsafeToDelete.length > 0) {
          message += ` ${unsafeToDelete.length} services were skipped due to dependencies.`
        }
        
        showSuccess('Services Deleted', message)
      } else {
        // All services are safe to delete
        showLoading('Deleting Services', `Deleting ${bulkServicesToDelete.length} services...`)
        
        const promises = bulkServicesToDelete.map(service => 
          fetch(`/api/admin/services/${service.id}`, { method: 'DELETE' })
        )
        
        await Promise.all(promises)
        showSuccess('Services Deleted', `Successfully deleted ${bulkServicesToDelete.length} services.`)
      }
      
      await fetchServices()
      setSelectedServices([])
      setShowBulkActions(false)
    } catch (error) {
      console.error('Error deleting services:', error)
      showError('Delete Error', 'Failed to delete selected services.')
    } finally {
      setIsDeleteModalOpen(false)
      setBulkServicesToDelete([])
      setIsBulkDelete(false)
      setVerificationData(null)
    }
  }, [bulkServicesToDelete, verificationData, showLoading, showSuccess, showWarning, fetchServices, showError])

  // Confirm and execute service deletion
  const confirmDeleteService = useCallback(async () => {
    if (isBulkDelete) {
      await executeBulkDelete()
      return
    }

    if (!serviceToDelete) return
    
    try {
      showLoading('Deleting Service', 'Removing service from the system...')
      
      const response = await fetch(`/api/admin/services/${serviceToDelete.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        showSuccess('Service Deleted', `"${serviceToDelete.name}" has been successfully removed.`)
        await fetchServices() // Refresh the services list
      } else {
        const errorData = await response.json()
        showError('Delete Failed', errorData.message || 'Failed to delete service')
      }
    } catch (error) {
      console.error('Error deleting service:', error)
      showError('Delete Error', 'An unexpected error occurred while deleting the service')
    } finally {
      setIsDeleteModalOpen(false)
      setServiceToDelete(null)
    }
  }, [isBulkDelete, serviceToDelete, executeBulkDelete, showLoading, showSuccess, showError, fetchServices])


  // Loading State
  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Main Render
  return (
    <div className="space-y-3" style={{ paddingBottom: dropdownSpaceNeeded > 0 ? `${dropdownSpaceNeeded}px` : '0' }}>
      {/* Mobile Layout */}
      <div className="flex flex-col space-y-3 lg:hidden">
        {/* Search Bar - Full Width on Mobile */}
        <div className="w-full">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
          </div>
        </div>

        {/* Mobile Controls - Single Row */}
        <div className="flex items-center gap-1">
          {/* View Mode Toggle - Stretched */}
          <div className="flex items-center bg-gray-100 rounded-lg p-0.5 flex-1">
            <button
              onClick={() => setViewMode('list')}
              className={`flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 ${
                viewMode === 'list'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              title="List view"
            >
              <ListBulletIcon className="h-3 w-3" />
              <span className="text-xs font-medium hidden xs:inline">List</span>
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 ${
                viewMode === 'grid'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              title="Grid view"
            >
              <Squares2X2Icon className="h-3 w-3" />
              <span className="text-xs font-medium hidden xs:inline">Grid</span>
            </button>
          </div>

          {/* Grid Columns Control (for grid view) */}
          {viewMode === 'grid' && (
            <div className="flex items-center bg-gray-100 rounded-lg p-0.5 flex-1">
              <span className="text-xs font-medium text-gray-700 px-1">Col:</span>
              <div className="flex items-center gap-0.5 flex-1">
                {[1, 2, 3, 4].map((num) => (
                  <button
                    key={num}
                    onClick={() => setGridColumns(num)}
                    className={`flex-1 px-1.5 py-1 rounded text-xs font-medium ${
                      gridColumns === num
                        ? 'bg-white text-green-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                    title={`${num} column${num > 1 ? 's' : ''}`}
                  >
                    {num}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Table Columns Control (for list view) */}
          {viewMode === 'list' && (
            <div className="relative flex-1">
              <button
                onClick={() => setShowColumnSelector(!showColumnSelector)}
                className="w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                title="Columns"
              >
                <ViewColumnsIcon className="h-3 w-3 mr-0.5" />
                <span className="hidden xs:inline">Col</span>
                <ChevronDownIcon className="h-3 w-3 ml-0.5" />
              </button>
            </div>
          )}

          {/* Filters Button - Stretched */}
          <div className="relative flex-1">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium rounded-lg border ${
                showFilters || Object.keys(currentFilters).some(key => currentFilters[key as keyof typeof currentFilters])
                  ? 'bg-blue-50 text-blue-700 border-blue-300'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              title="Filters"
            >
              <FunnelIcon className="h-3 w-3 mr-0.5" />
              <span className="hidden xs:inline">Filter</span>
              {Object.keys(currentFilters).some(key => currentFilters[key as keyof typeof currentFilters]) && (
                <span className="ml-0.5 inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {Object.values(currentFilters).filter(Boolean).length}
                </span>
              )}
            </button>
          </div>

          {/* Density Control - Stretched */}
          <div className="relative flex-1">
            <button
              onClick={() => setShowWindowList(!showWindowList)}
              className="w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
              title="Density"
            >
              <AdjustmentsHorizontalIcon className="h-3 w-3 mr-0.5" />
              <span className="hidden xs:inline">{density.charAt(0).toUpperCase() + density.slice(1)}</span>
              <ChevronDownIcon className="h-3 w-3 ml-0.5" />
            </button>
          </div>

          {/* Create Button - Stretched */}
          <button
            onClick={handleCreateService}
            className="flex-1 inline-flex items-center justify-center px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="h-3 w-3 mr-0.5" />
            <span className="hidden xs:inline">Add</span>
          </button>
        </div>
      </div>

      {/* Desktop Layout - Horizontal */}
      <div className="hidden lg:flex items-center justify-between gap-4 mb-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm">
        {/* Search Bar and Filters */}
        <div className="flex items-center gap-3 flex-1 max-w-md">
          <div className="relative flex-1">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search services by name or description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
          />
          </div>

          {/* Filters Button */}
          <div className="relative dropdown-container">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              title="Filter services"
            >
              <FunnelIcon className="h-4 w-4 mr-2" />
              Filters
              {Object.keys(currentFilters).some(key => currentFilters[key as keyof typeof currentFilters]) && (
                <span className="ml-2 inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-blue-600 rounded-full">
                  {Object.values(currentFilters).filter(Boolean).length}
                </span>
              )}
              <ChevronDownIcon className="h-4 w-4 ml-2" />
            </button>
            {showFilters && (
              <div className="hidden lg:block absolute top-full right-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                <div className="p-4">
                  <h3 className="text-sm font-medium text-gray-900 mb-3">Filter Services</h3>
                  
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select
                      value={currentFilters.status || ''}
                      onChange={(e) => setCurrentFilters(prev => ({ ...prev, status: e.target.value || undefined }))}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">All Statuses</option>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
                    <select
                      value={currentFilters.priceRange || ''}
                      onChange={(e) => setCurrentFilters(prev => ({ ...prev, priceRange: e.target.value || undefined }))}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">All Prices</option>
                      <option value="free">Free</option>
                      <option value="low">$1 - $100</option>
                      <option value="medium">$101 - $500</option>
                      <option value="high">$500+</option>
                    </select>
                  </div>

                  <div className="flex justify-end mt-4 pt-4 border-t border-gray-200">
                    <button
                      onClick={() => {
                        setCurrentFilters({})
                        setShowFilters(false)
                      }}
                      className="px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg"
                    >
                      Clear All
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* View Options and Controls */}
        <div className="flex items-center gap-3">
          {/* View Mode Toggle */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">View:</span>
            <div className="flex items-center bg-gray-100 rounded-lg p-1 gap-1">
              <button
                onClick={() => setViewMode('list')}
                className={`px-3 py-2 rounded-md flex items-center gap-2 ${
                  viewMode === 'list'
                    ? 'bg-white text-green-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="List view"
              >
                <ListBulletIcon className="h-5 w-5" />
                <span className="text-sm font-medium">List</span>
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`px-3 py-2 rounded-md flex items-center gap-2 ${
                  viewMode === 'grid'
                    ? 'bg-white text-green-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Grid view"
              >
                <Squares2X2Icon className="h-5 w-5" />
                <span className="text-sm font-medium">Grid</span>
              </button>
            </div>
          </div>

          {/* Grid Columns Control (for grid view) */}
          {viewMode === 'grid' && (
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700">Columns:</span>
              <div className="flex items-center bg-gray-100 rounded-lg p-1 gap-1">
                <button
                  onClick={() => setGridColumns(1)}
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    gridColumns === 1
                      ? 'bg-white text-green-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="1 column"
                >
                  1
                </button>
                <button
                  onClick={() => setGridColumns(2)}
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    gridColumns === 2
                      ? 'bg-white text-green-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="2 columns"
                >
                  2
                </button>
                <button
                  onClick={() => setGridColumns(3)}
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    gridColumns === 3
                      ? 'bg-white text-green-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="3 columns"
                >
                  3
                </button>
                <button
                  onClick={() => setGridColumns(4)}
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    gridColumns === 4
                      ? 'bg-white text-green-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="4 columns"
                >
                  4
                </button>
              </div>
            </div>
          )}

          {/* Column Selector (List View Only) */}
          {viewMode === 'list' && (
            <div className="relative dropdown-container">
              <button
                onClick={() => setShowColumnSelector(!showColumnSelector)}
                className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                title="Select columns to display"
              >
                <ViewColumnsIcon className="h-4 w-4 mr-2" />
                Columns
                <ChevronDownIcon className="h-4 w-4 ml-2" />
              </button>
              {showColumnSelector && (
                <div className="hidden lg:block absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                  <div className="p-2">
                    {Object.entries(visibleColumns).map(([key, visible]) => (
                      <label key={key} className="flex items-center space-x-2 py-1">
                        <input
                          type="checkbox"
                          checked={visible}
                          onChange={(e) => setVisibleColumns(prev => ({ ...prev, [key]: e.target.checked }))}
                          className="h-4 w-4 text-green-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="text-sm text-gray-700 capitalize">{key}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Density Control */}
          <div className="relative dropdown-container">
            <button
              onClick={() => setShowWindowList(!showWindowList)}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              title="Adjust table density"
            >
              <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
              {density.charAt(0).toUpperCase() + density.slice(1)}
              <ChevronDownIcon className="h-4 w-4 ml-2" />
            </button>
            {showWindowList && (
              <div className="hidden lg:block absolute top-full left-0 mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                <div className="p-1">
                  {(['compact', 'comfortable', 'spacious'] as const).map((option) => (
                    <button
                      key={option}
                      onClick={() => {
                        handleDensityChange(option)
                        setShowWindowList(false)
                      }}
                      className={`w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100 ${
                        density === option ? 'bg-green-100 text-blue-700' : 'text-gray-700'
                      }`}
                    >
                      {option.charAt(0).toUpperCase() + option.slice(1)}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>


          {/* Create Button */}
          <button
            onClick={handleCreateService}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Service
          </button>
        </div>
      </div>

      {/* Mobile Dropdowns */}
      {/* Filters Dropdown - Mobile */}
      {showFilters && (
        <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-900">Filter Services</h3>
            <button
              onClick={() => setShowFilters(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <select
                value={currentFilters.status || ''}
                onChange={(e) => setCurrentFilters(prev => ({ ...prev, status: e.target.value || undefined }))}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
              <select
                value={currentFilters.priceRange || ''}
                onChange={(e) => setCurrentFilters(prev => ({ ...prev, priceRange: e.target.value || undefined }))}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Prices</option>
                <option value="free">Free</option>
                <option value="low">$1 - $100</option>
                <option value="medium">$101 - $500</option>
                <option value="high">$500+</option>
              </select>
            </div>
          </div>
          
          <div className="flex justify-end mt-4 pt-4 border-t border-gray-200">
            <button
              onClick={() => {
                setCurrentFilters({})
                setShowFilters(false)
              }}
              className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg"
            >
              Clear All
            </button>
          </div>
        </div>
      )}

      {/* Column Selector Dropdown - Mobile */}
      {showColumnSelector && (
        <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4">
          <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Show Columns</div>
          {Object.entries(visibleColumns).map(([key, visible]) => (
            <label key={key} className="flex items-center space-x-2 py-1">
              <input
                type="checkbox"
                checked={visible}
                onChange={(e) => setVisibleColumns(prev => ({ ...prev, [key]: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700 capitalize">{key}</span>
            </label>
          ))}
        </div>
      )}

      {/* Density Dropdown - Mobile */}
      {showWindowList && (
        <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-2">
          {(['compact', 'comfortable', 'spacious'] as const).map((option) => (
            <button
              key={option}
              onClick={() => {
                setDensity(option)
                setShowWindowList(false)
              }}
              className={`w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 ${
                density === option ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'
              }`}
            >
              {option.charAt(0).toUpperCase() + option.slice(1)}
            </button>
          ))}
        </div>
      )}

      {/* Services Content */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading services...</p>
          </div>
        ) : filteredServices.length === 0 ? (
          <div className="p-6 text-center">
            <CogIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No services found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm ? 'Try adjusting your search terms.' : 'Get started by creating your first service.'}
            </p>
            <button
              onClick={handleCreateService}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Service
            </button>
          </div>
        ) : (
          <div>
            {viewMode === 'list' && (
              <div className="overflow-hidden">
                {/* Bulk Actions Bar */}
                {showBulkActions && selectedServices.length > 0 && (
                  <div className="bg-green-50 border border-green-200 rounded-lg px-4 py-2 shadow-sm">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                            <span className="text-green-600 font-semibold text-xs">
                              {selectedServices.length}
                            </span>
                          </div>
                          <span className="text-xs font-medium text-green-900">
                            {selectedServices.length === 1 ? 'service' : 'services'} selected
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-1.5">
                          <button
                            onClick={handleBulkActivate}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 border border-green-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                            title="Activate selected services"
                          >
                            <EyeIcon className="h-3 w-3 mr-1" />
                            Activate
                          </button>
                          
                          <button
                            onClick={handleBulkDeactivate}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500"
                            title="Deactivate selected services"
                          >
                            <EyeSlashIcon className="h-3 w-3 mr-1" />
                            Deactivate
                          </button>
                          
                          <button
                            onClick={() => showBulkDeleteConfirmation(selectedServices)}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 border border-red-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500"
                            title="Delete selected services"
                          >
                            <TrashIcon className="h-3 w-3 mr-1" />
                            Delete
                          </button>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-1.5">
                        <button
                          onClick={handleClearSelection}
                          className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gray-500"
                          title="Clear selection"
                        >
                          <XMarkIcon className="h-3 w-3 mr-1" />
                          Clear
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                <div className={showBulkActions && selectedServices.length > 0 ? "mt-3" : ""}>
                  <table className="min-w-full">
                  <thead className="bg-gray-200 border-b border-gray-300">
                    <tr>
                      <th scope="col" className="relative pl-2 py-2" style={{ width: '6px' }}>
                        <input
                          type="checkbox"
                          checked={selectedServices.length === filteredServices.length && filteredServices.length > 0}
                          onChange={handleSelectAll}
                          className={`text-green-600 focus:ring-blue-500 border-gray-300 rounded ${
                            density === 'compact' 
                              ? 'h-4 w-4' 
                              : density === 'spacious' 
                              ? 'h-5 w-5' 
                              : 'h-4 w-4'
                          }`}
                        />
                      </th>
                      {visibleColumns.name && (
                        <th scope="col" className="pl-6 pr-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-24 py-2 text-xs" onClick={() => handleSort('name')}>
                          <div className="flex items-center space-x-1">
                            <span>Service</span>
                            {getSortIcon('name')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.description && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-80 py-2 text-xs" onClick={() => handleSort('description')}>
                          <div className="flex items-center space-x-1">
                            <span>Description</span>
                            {getSortIcon('description')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.price && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('price')}>
                          <div className="flex items-center space-x-1">
                            <span>Price</span>
                            {getSortIcon('price')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.discountRate && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('discountRate')}>
                          <div className="flex items-center space-x-1">
                            <span>Discount Rate</span>
                            {getSortIcon('discountRate')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.totalDiscount && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('totalDiscount')}>
                          <div className="flex items-center space-x-1">
                            <span>Total Discount</span>
                            {getSortIcon('totalDiscount')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.manager && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('manager')}>
                          <div className="flex items-center space-x-1">
                            <span>Manager</span>
                            {getSortIcon('manager')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.options && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('options')}>
                          <div className="flex items-center space-x-1">
                            <span>Options</span>
                            {getSortIcon('options')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.status && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 py-2 text-xs" onClick={() => handleSort('isActive')}>
                          <div className="flex items-center space-x-1">
                            <span>Status</span>
                            {getSortIcon('isActive')}
                          </div>
                        </th>
                      )}
                      {visibleColumns.actions && (
                        <th scope="col" className="px-6 text-left font-medium text-gray-900 uppercase tracking-wider py-2 text-xs">
                          <span>Actions</span>
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                    {paginatedServices.map((service) => (
                      <ServiceRow
                        key={service.id}
                        service={service}
                        isSelected={selectedService?.id === service.id}
                        onServiceSelect={handleServiceSelect}
                        onEdit={handleEditService}
                        onDelete={handleDeleteService}
                        isChecked={selectedServices.includes(service.id)}
                        onCheck={handleSelectService}
                      />
                    ))}
                  </tbody>
                </table>
                </div>
              </div>
            )}

            {viewMode === 'grid' && (
              <div>
                {/* Bulk Actions Bar for Grid View */}
                {showBulkActions && selectedServices.length > 0 && (
                  <div className="bg-green-50 border border-green-200 rounded-lg px-4 py-2 shadow-sm">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                            <span className="text-green-600 font-semibold text-xs">
                              {selectedServices.length}
                            </span>
                          </div>
                          <span className="text-xs font-medium text-green-900">
                            service{selectedServices.length === 1 ? '' : 's'} selected
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-1.5">
                          <button
                            onClick={handleBulkActivate}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 border border-green-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                            title="Activate selected services"
                          >
                            <EyeIcon className="h-3 w-3 mr-1" />
                            Activate
                          </button>
                          
                          <button
                            onClick={handleBulkDeactivate}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500"
                            title="Deactivate selected services"
                          >
                            <EyeSlashIcon className="h-3 w-3 mr-1" />
                            Deactivate
                          </button>
                          
                          <button
                            onClick={() => showBulkDeleteConfirmation(selectedServices)}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 border border-red-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500"
                            title="Delete selected services"
                          >
                            <TrashIcon className="h-3 w-3 mr-1" />
                            Delete
                          </button>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-1.5">
                        <button
                          onClick={handleClearSelection}
                          className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gray-500"
                          title="Clear selection"
                        >
                          <XMarkIcon className="h-3 w-3 mr-1" />
                          Clear
                        </button>
                      </div>
                    </div>
                  </div>
                )}
                
                <div className={showBulkActions && selectedServices.length > 0 ? "mt-3" : ""}>
                  <div className={`grid gap-4 ${
                  gridColumns === 1 ? 'grid-cols-1' :
                  gridColumns === 2 ? 'grid-cols-1 md:grid-cols-2' :
                  gridColumns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
                  'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                }`}>
                {paginatedServices.map((service) => {
                  // Apply density-based padding
                  const getDensityPadding = () => {
                    switch (density) {
                      case 'compact': return 'p-3'
                      case 'comfortable': return 'p-4'
                      case 'spacious': return 'p-5'
                      default: return 'p-4'
                    }
                  }

                  return (
                  <div
                    key={service.id}
                    className={`group relative bg-white border border-gray-200 rounded-lg cursor-pointer transition-all duration-300 overflow-hidden hover:cursor-pointer shadow-sm hover:shadow-md ${
                      selectedService?.id === service.id
                        ? 'ring-2 ring-blue-500 ring-offset-2 bg-blue-50/50 border-blue-300'
                        : 'hover:bg-gray-50/50 hover:border-gray-300'
                    } ${
                      selectedServices.includes(service.id) 
                        ? 'bg-green-50 border-l-4 border-green-500' 
                        : ''
                    } ${getDensityPadding()}`}
                    onClick={() => handleServiceSelect(service)}
                    onMouseEnter={(e) => {
                      // Only show action menu on hover for desktop (lg and up)
                      if (window.innerWidth >= 1024) {
                        const actionMenu = e.currentTarget.querySelector('.action-menu') as HTMLElement;
                        if (actionMenu) {
                          actionMenu.style.opacity = '1';
                          actionMenu.style.transform = 'translateX(0)';
                          actionMenu.style.pointerEvents = 'auto';
                        }
                      }
                    }}
                    onMouseLeave={(e) => {
                      // Only hide action menu on mouse leave for desktop (lg and up)
                      if (window.innerWidth >= 1024) {
                        const actionMenu = e.currentTarget.querySelector('.action-menu') as HTMLElement;
                        if (actionMenu) {
                          actionMenu.style.opacity = '0';
                          actionMenu.style.transform = 'translateX(100%)';
                          actionMenu.style.pointerEvents = 'none';
                        }
                      }
                    }}
                  >
                    {/* Header Section */}
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-start space-x-2 flex-1 min-w-0">
                        {/* Checkbox */}
                        <div className="flex-shrink-0 pt-1" onClick={(e) => e.stopPropagation()}>
                          <input
                            type="checkbox"
                            checked={selectedServices.includes(service.id)}
                            onChange={(e) => {
                              e.stopPropagation()
                              handleSelectService(service.id)
                            }}
                            onClick={(e) => e.stopPropagation()}
                            className={`h-4 w-4 text-green-600 focus:ring-blue-500 border-gray-300 rounded ${
                              density === 'compact' 
                                ? 'h-3 w-3' 
                                : density === 'spacious' 
                                ? 'h-5 w-5' 
                                : 'h-4 w-4'
                            }`}
                          />
                        </div>
                        {/* Icon Container */}
                        <div className="flex-shrink-0 p-2 bg-blue-50 rounded-lg">
                          <i className={`fas ${getServiceIcon(service)} text-2xl`}></i>
                        </div>
                        
                        {/* Title and Description */}
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-base text-gray-900 truncate mb-2">
                            {service.name}
                          </h3>
                          {service.description && (
                            <p className="text-sm text-gray-600 line-clamp-2 leading-relaxed">
                              {service.description}
                            </p>
                          )}
                        </div>
                      </div>

                      {/* Status Badge */}
                      <span className={`inline-flex items-center px-2.5 py-0 rounded-full text-xs font-medium ${
                        service.isActive
                          ? 'bg-green-100 text-green-800 border border-green-200'
                          : 'bg-red-100 text-red-800 border border-red-200'
                      }`}>
                        <div className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
                          service.isActive ? 'bg-green-400' : 'bg-red-400'
                        }`}></div>
                        {service.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>

                    {/* Divider */}
                    <div className="border-t border-gray-100 my-1"></div>

                    {/* Info Grid */}
                    {/* Pricing Information */}
                    <div className={`${
                      density === 'compact' ? 'space-y-2 mb-2' : density === 'spacious' ? 'space-y-4 mb-6' : 'space-y-3 mb-4'
                    }`}>
                      {/* Pricing Grid */}
                      <div className="grid grid-cols-2 gap-3">
                        {/* Base Price */}
                        <div className={`bg-gray-50 rounded-lg ${
                          density === 'compact' 
                            ? 'p-2' 
                            : density === 'spacious' 
                            ? 'p-4' 
                            : 'p-3'
                        }`}>
                          <div className="flex items-center space-x-2 mb-1">
                            <div className={`bg-white rounded-lg flex items-center justify-center shadow-sm ${
                              density === 'compact' 
                                ? 'w-5 h-5' 
                                : density === 'spacious' 
                                ? 'w-8 h-8' 
                                : 'w-6 h-6'
                            }`}>
                              <i className={`fas fa-dollar-sign text-green-600 ${
                                density === 'compact' 
                                  ? 'text-xs' 
                                  : density === 'spacious' 
                                  ? 'text-sm' 
                                  : 'text-xs'
                              }`}></i>
                        </div>
                            <div className={`font-medium text-gray-500 uppercase tracking-wide ${
                              density === 'compact' 
                                ? 'text-xs' 
                                : density === 'spacious' 
                                ? 'text-sm' 
                                : 'text-xs'
                            }`}>
                              Base Price
                            </div>
                          </div>
                          <div className={`font-bold text-gray-900 ${
                            density === 'compact' 
                              ? 'text-sm' 
                              : density === 'spacious' 
                              ? 'text-lg' 
                              : 'text-base'
                          }`}>
                            ${service.price || 0}
                          </div>
                        </div>

                        {/* Discount Rate */}
                        <div className={`bg-gray-50 rounded-lg ${
                          density === 'compact' 
                            ? 'p-2' 
                            : density === 'spacious' 
                            ? 'p-4' 
                            : 'p-3'
                        }`}>
                          <div className="flex items-center space-x-2 mb-1">
                            <div className={`bg-white rounded-lg flex items-center justify-center shadow-sm ${
                              density === 'compact' 
                                ? 'w-5 h-5' 
                                : density === 'spacious' 
                                ? 'w-8 h-8' 
                                : 'w-6 h-6'
                            }`}>
                              <i className={`fas fa-percentage text-blue-600 ${
                                density === 'compact' 
                                  ? 'text-xs' 
                                  : density === 'spacious' 
                                  ? 'text-sm' 
                                  : 'text-xs'
                              }`}></i>
                            </div>
                            <div className={`font-medium text-gray-500 uppercase tracking-wide ${
                              density === 'compact' 
                                ? 'text-xs' 
                                : density === 'spacious' 
                                ? 'text-sm' 
                                : 'text-xs'
                            }`}>
                              Discount Rate
                            </div>
                          </div>
                          <div className={`font-bold ${
                            service.discountRate && service.discountRate > 0 
                              ? 'text-blue-600' 
                              : 'text-gray-400'
                          } ${
                            density === 'compact' 
                              ? 'text-sm' 
                              : density === 'spacious' 
                              ? 'text-lg' 
                              : 'text-base'
                          }`}>
                            {service.discountRate || 0}%
                          </div>
                        </div>
                      </div>

                      {/* Total Discount (if applicable) */}
                      {(() => {
                        const basePrice = service.price || 0;
                        const discountRate = service.discountRate || 0;
                        const calculatedDiscount = basePrice * (discountRate / 100);
                        return calculatedDiscount > 0 && (
                          <div className={`bg-red-50 rounded-lg border border-red-100 ${
                            density === 'compact' 
                              ? 'p-2' 
                              : density === 'spacious' 
                              ? 'p-4' 
                              : 'p-3'
                          }`}>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <div className={`bg-white rounded-lg flex items-center justify-center shadow-sm ${
                                  density === 'compact' 
                                    ? 'w-5 h-5' 
                                    : density === 'spacious' 
                                    ? 'w-8 h-8' 
                                    : 'w-6 h-6'
                                }`}>
                                  <i className={`fas fa-tag text-red-600 ${
                                    density === 'compact' 
                                      ? 'text-xs' 
                                      : density === 'spacious' 
                                      ? 'text-sm' 
                                      : 'text-xs'
                                  }`}></i>
                                </div>
                                <div>
                                  <div className={`font-medium text-gray-500 uppercase tracking-wide ${
                                    density === 'compact' 
                                      ? 'text-xs' 
                                      : density === 'spacious' 
                                      ? 'text-sm' 
                                      : 'text-xs'
                                  }`}>
                                    Total Discount
                                  </div>
                                  <div className={`font-bold text-red-600 ${
                                    density === 'compact' 
                                      ? 'text-sm' 
                                      : density === 'spacious' 
                                      ? 'text-lg' 
                                      : 'text-base'
                                  }`}>
                                    -${calculatedDiscount.toFixed(2)}
                                  </div>
                                </div>
                              </div>
                              {/* Final Price Calculation */}
                              <div className="text-right">
                                <div className={`font-medium text-gray-500 uppercase tracking-wide ${
                                  density === 'compact' 
                                    ? 'text-xs' 
                                    : density === 'spacious' 
                                    ? 'text-sm' 
                                    : 'text-xs'
                                }`}>
                                  Final Price
                                </div>
                                <div className={`font-bold text-green-600 ${
                                  density === 'compact' 
                                    ? 'text-sm' 
                                    : density === 'spacious' 
                                    ? 'text-lg' 
                                    : 'text-base'
                                }`}>
                                  ${(basePrice - calculatedDiscount).toFixed(2)}
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })()}
                      </div>

                      {/* Options Count */}
                      <div className="space-y-0.5">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Options</div>
                        <div className="flex items-center space-x-2">
                        <span className="inline-flex items-center px-1 py-0 bg-green-100 text-blue-800 text-sm font-medium rounded-md">
                            {service._count?.serviceOptions || 0}
                          </span>
                      </div>
                    </div>

                    {/* Additional Info */}
                    <div className="flex items-center justify-between text-xs text-gray-500 pt-1 border-t border-gray-100">
                      <span className="flex items-center space-x-1">
                        <span className="font-medium">Manager:</span>
                        <span className="bg-gray-100 px-1 py-0 rounded">{service.manager || 'N/A'}</span>
                      </span>
                      <div className="flex items-center space-x-2">
                        <span className="flex items-center space-x-1">
                          <span className="font-medium">Order:</span>
                          <span>{service.displayOrder}</span>
                        </span>
                        
                        {/* Mobile Action Button - Always visible on mobile and small screens */}
                        <div className="lg:hidden">
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              const actionMenu = e.currentTarget.closest('.group')?.querySelector('.action-menu') as HTMLElement;
                              if (actionMenu) {
                                const isVisible = actionMenu.style.opacity === '1';
                                actionMenu.style.opacity = isVisible ? '0' : '1';
                                actionMenu.style.transform = isVisible ? 'translateX(100%)' : 'translateX(0)';
                                actionMenu.style.pointerEvents = isVisible ? 'none' : 'auto';
                              }
                            }}
                            className="p-2 text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                            title="Show Actions"
                          >
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Actions Sidebar - Professional Overlay */}
                    <div className={`action-menu absolute bg-white rounded-lg border border-gray-200 flex flex-col items-center justify-center transition-all duration-200 ${
                      density === 'compact' 
                        ? 'top-2 right-2 bottom-2 w-10 space-y-2' 
                        : density === 'spacious' 
                        ? 'top-4 right-4 bottom-4 w-14 space-y-6' 
                        : 'top-3 right-3 bottom-3 w-12 space-y-4'
                    }`} style={{
                      opacity: '0',
                      transform: 'translateX(100%)',
                      pointerEvents: 'none'
                    }}>
                      {/* Edit Button */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEditService(service)
                        }}
                        className={`flex items-center justify-center bg-blue-500 text-white rounded-lg border border-blue-600 hover:scale-110 shadow-sm pointer-events-auto ${
                          density === 'compact' 
                            ? 'w-7 h-7' 
                            : density === 'spacious' 
                            ? 'w-10 h-10' 
                            : 'w-8 h-8'
                        }`}
                        title="Edit Service"
                      >
                        <PencilIcon className={`${
                          density === 'compact' 
                            ? 'h-3 w-3' 
                            : density === 'spacious' 
                            ? 'h-5 w-5' 
                            : 'h-4 w-4'
                        }`} />
                      </button>
                      
                      {/* Toggle Active Button */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          // Toggle active functionality would go here
                        }}
                        className={`flex items-center justify-center rounded-lg border hover:scale-110 shadow-sm pointer-events-auto ${
                          density === 'compact' 
                            ? 'w-7 h-7' 
                            : density === 'spacious' 
                            ? 'w-10 h-10' 
                            : 'w-8 h-8'
                        } ${
                          service.isActive
                            ? 'bg-emerald-500 border-emerald-600 text-white'
                            : 'bg-gray-400 border-gray-500 text-white'
                        }`}
                        title={service.isActive ? 'Deactivate Service' : 'Activate Service'}
                      >
                        <i className={`fas fa-power-off ${
                          density === 'compact' 
                            ? 'text-xs' 
                            : density === 'spacious' 
                            ? 'text-sm' 
                            : 'text-xs'
                        }`} />
                      </button>
                      
                      {/* Delete Button */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeleteService(service)
                        }}
                        className={`flex items-center justify-center bg-red-500 text-white rounded-lg border border-red-600 hover:scale-110 shadow-sm pointer-events-auto ${
                          density === 'compact' 
                            ? 'w-7 h-7' 
                            : density === 'spacious' 
                            ? 'w-10 h-10' 
                            : 'w-8 h-8'
                        }`}
                        title="Delete Service"
                      >
                        <TrashIcon className={`${
                          density === 'compact' 
                            ? 'h-3 w-3' 
                            : density === 'spacious' 
                            ? 'h-5 w-5' 
                            : 'h-4 w-4'
                        }`} />
                      </button>
                    </div>
                  </div>
                  )
                })}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Responsive Pagination */}
      {filteredServices.length > 0 && (
        <ResponsivePagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          itemsPerPage={itemsPerPage}
          onItemsPerPageChange={handleItemsPerPageChange}
          totalItems={filteredServices.length}
          startIndex={startIndex}
          endIndex={endIndex}
          itemsPerPageOptions={[5, 10, 20, 50]}
          showItemsPerPage={true}
          showPageInfo={true}
        />
      )}

      {/* Form Modal */}
      <ServiceFormModal
        isOpen={isFormOpen}
        onClose={() => {
                      setIsFormOpen(false)
                      if (editingService) {
                        showInfo('Edit Cancelled', `Editing "${editingService.name}" was cancelled.`)
                      } else {
                        showInfo('Create Cancelled', 'Service creation was cancelled.')
                      }
                    }}
        onSubmit={async (formData) => {
          try {
            const url = editingService 
              ? `/api/admin/services/${editingService.id}`
              : '/api/admin/services'
            
            const method = editingService ? 'PUT' : 'POST'
            
            const response = await fetch(url, {
              method,
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                servicename: formData.name,
                servicedesc: formData.description,
                categoryid: category.id,
                price: formData.price,
                discountrate: formData.discountRate,
                totaldiscount: formData.totalDiscount || (formData.discountRate > 0 ? (formData.price * formData.discountRate) / 100 : 0),
                manager: formData.manager,
                isactive: formData.isActive,
                displayorder: formData.displayOrder,
                iconclass: formData.iconClass
              })
            })

            if (response.ok) {
              console.log('Service saved successfully, showing notification...')
              // Show success notification
              if (editingService) {
                console.log('Showing update success notification')
                showSuccess('Service Updated', `"${editingService.name}" has been successfully updated.`)
              } else {
                console.log('Showing create success notification')
                showSuccess('Service Created', `"${formData.name}" has been successfully created.`)
              }
              
              // Reset form after successful submission
              resetForm()
              fetchServices()
            } else {
              const errorData = await response.json()
              showError('Save Failed', errorData.message || 'Failed to save service')
            }
          } catch (error) {
            console.error('Error saving service:', error)
            showError('Save Error', 'An unexpected error occurred while saving the service')
          }
        }}
        editingService={editingService}
        categoryId={category.id}
      />


      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        title={isBulkDelete ? (bulkServicesToDelete.length === 1 ? "Delete Service" : "Delete Multiple Services") : "Delete Service"}
        message={(() => {
          if (isBulkDelete) {
            const count = bulkServicesToDelete.length
            if (verificationData?.canDelete) {
              return count === 1 
                ? `Are you sure you want to delete "${bulkServicesToDelete[0].name}"?`
                : `Are you sure you want to delete ${count} services?`
            } else {
              return count === 1 
                ? `Cannot delete "${bulkServicesToDelete[0].name}"`
                : `Cannot delete ${count} services`
            }
          }
          
          if (!verificationData) return `Are you sure you want to delete "${serviceToDelete?.name}"?`
          
          if (verificationData.canDelete) {
            const hasOptions = (verificationData.serviceOptions || 0) > 0
            const hasOrders = (verificationData.orderDetails || 0) > 0
            
            if (hasOptions && hasOrders) {
              return `Delete "${serviceToDelete?.name}" and all its data?`
            } else if (hasOptions) {
              return `Delete "${serviceToDelete?.name}" and its service options?`
            } else if (hasOrders) {
              return `Delete "${serviceToDelete?.name}" (has order history)?`
            } else {
              return `Delete "${serviceToDelete?.name}"?`
            }
          } else {
            return `Cannot delete "${serviceToDelete?.name}"`
          }
        })()}
        details={(() => {
          if (isBulkDelete) {
            const count = bulkServicesToDelete.length
            if (verificationData?.canDelete) {
              return `This will permanently remove ${count} ${count === 1 ? 'service' : 'services'} and all associated data. This action cannot be undone.`
            } else {
              const totalServiceOptions = verificationData?.serviceOptions || 0
              const totalOrderDetails = verificationData?.orderDetails || 0
              return `Some services have dependencies. Only services without service options or orders will be deleted. This will skip ${totalServiceOptions + totalOrderDetails} dependencies.`
            }
          }
          
          if (!verificationData) return "This action cannot be undone. All associated data will be permanently removed."
          
          if (verificationData.canDelete) {
            const hasOptions = (verificationData.serviceOptions || 0) > 0
            const hasOrders = (verificationData.orderDetails || 0) > 0
            
            if (hasOptions && hasOrders) {
              return "This will permanently remove the service, all its options, features, and order history. This action cannot be undone."
            } else if (hasOptions) {
              return "This will permanently remove the service and all its service options. This action cannot be undone."
            } else if (hasOrders) {
              return "This service has order history. Deleting it will remove all associated order records. This action cannot be undone."
            } else {
              return "This service has no dependencies. It will be permanently removed. This action cannot be undone."
            }
          } else {
            return "Please remove all dependencies before attempting to delete this service."
          }
        })()}
        confirmText={verificationData?.canDelete || isBulkDelete ? "Delete" : "Delete Safe Only"}
        cancelText="Cancel"
        onConfirm={confirmDeleteService}
        onCancel={() => {
          setIsDeleteModalOpen(false)
          setServiceToDelete(null)
          setBulkServicesToDelete([])
          setIsBulkDelete(false)
          setVerificationData(null)
          setIsVerifying(false)
          // Clear any remaining loading notifications when modal is cancelled
          clearLoadingNotifications()
        }}
        type={verificationData?.canDelete || isBulkDelete ? 'danger' : 'verification'}
        showVerification={true}
        verificationData={verificationData ? {
          canDelete: verificationData.canDelete,
          reason: verificationData.reason,
          dependencies: verificationData.dependencies
        } : undefined}
      />
    </div>
  )
}

// Main Services Management Component
// Orchestrates the entire service management workflow with navigation between sections
export function ServicesManagement() {
  // Navigation and Selection State
  const [activeSection, setActiveSection] = useState<ActiveSection>('categories')
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [selectedService, setSelectedService] = useState<Service | null>(null)
  const [selectedOption, setSelectedOption] = useState<ServiceOption | null>(null)

  // Counter states for database-fetched counts
  const [categoriesCount, setCategoriesCount] = useState<number>(0)
  const [optionsCount, setOptionsCount] = useState<number>(0)
  const [featuresCount, setFeaturesCount] = useState<number>(0)

  // Notifications system - using centralized provider
  const { showSuccess, showError, showWarning, showInfo, showLoading, clearLoadingNotifications } = useNotifications()

  // Data Fetching Functions
  // Fetch categories count for navigation display
  const fetchCategoriesCount = useCallback(async () => {
    try {
      const response = await fetch('/api/admin/categories')
      if (response.ok) {
        const data = await response.json()
        if (data.data) {
          setCategoriesCount(data.data.length || 0)
        }
      }
    } catch (error) {
      console.error('Error fetching categories count:', error)
      setCategoriesCount(0)
    }
  }, [])

  // Fetch options count for selected service
  const fetchOptionsCount = useCallback(async (serviceId: string) => {
    try {
      const response = await fetch(`/api/admin/service-options?serviceId=${serviceId}`)
      if (response.ok) {
        const data = await response.json()
        if (data.data) {
          setOptionsCount(data.data.length || 0)
        }
      }
    } catch (error) {
      console.error('Error fetching options count:', error)
      setOptionsCount(0)
    }
  }, [])

  // Fetch features count for selected option
  const fetchFeaturesCount = useCallback(async (optionId: string) => {
    try {
      const response = await fetch(`/api/admin/option-features?optionId=${optionId}`)
      if (response.ok) {
        const data = await response.json()
        if (data.data) {
          setFeaturesCount(data.data.length || 0)
        }
      }
    } catch (error) {
      console.error('Error fetching features count:', error)
      setFeaturesCount(0)
    }
  }, [])

  // Effects
  // Fetch categories count on component mount
  useEffect(() => {
    fetchCategoriesCount()
  }, [fetchCategoriesCount])

  // Helper Functions
  // Counter function for navigation cards - provides real-time counts
  const getCounter = useCallback((sectionId: string) => {
    switch (sectionId) {
      case 'categories':
        return categoriesCount
      case 'services':
        return selectedCategory ? (selectedCategory._count?.services || 0) : 0
      case 'options':
        return selectedService ? optionsCount : 0
      case 'features':
        return selectedOption ? featuresCount : 0
      default:
        return 0
    }
  }, [categoriesCount, selectedCategory, optionsCount, selectedService, featuresCount, selectedOption])

  // Navigation Configuration
  // Define sections with their properties and dependencies
  const sections = [
    {
      id: 'categories' as const,
      title: 'Categories',
      description: 'Organize and structure your service categories with hierarchical management',
      color: 'bg-blue-500',
      gradient: 'bg-gradient-to-r from-blue-600 to-blue-700',
      icon: BuildingOfficeIcon,
      isActive: activeSection === 'categories',
      disabled: false,
      selectedName: selectedCategory?.name || null
    },
    {
      id: 'services' as const,
      title: 'Services',
      description: 'Define and configure individual services within your categories',
      color: 'bg-emerald-500',
      gradient: 'bg-gradient-to-r from-emerald-600 to-emerald-700',
      icon: Cog6ToothIcon,
      isActive: activeSection === 'services',
      disabled: !selectedCategory,
      selectedName: selectedService?.name || null
    },
    {
      id: 'options' as const,
      title: 'Service Options',
      description: 'Create customizable options and variations for your services',
      color: 'bg-amber-500',
      gradient: 'bg-gradient-to-r from-amber-600 to-amber-700',
      icon: RectangleStackIcon,
      isActive: activeSection === 'options',
      disabled: !selectedService,
      selectedName: selectedOption?.name || null
    },
    {
      id: 'features' as const,
      title: 'Option Features',
      description: 'Add detailed features and specifications to service options',
      color: 'bg-purple-500',
      gradient: 'bg-gradient-to-r from-purple-600 to-purple-700',
      icon: SparklesIcon,
      isActive: activeSection === 'features',
      disabled: !selectedOption,
      selectedName: null // Features don't have a single selected item
    }
  ] as const

  // Navigation Handlers
  // Handle section navigation with validation
  const handleSectionChange = useCallback((sectionId: ActiveSection) => {
    if (sections.find(s => s.id === sectionId)?.disabled) return
    setActiveSection(sectionId)
  }, [sections])

  // Handle category selection with cascading resets
  const handleCategorySelect = useCallback((category: Category | null) => {
    setSelectedCategory(category)
    setSelectedService(null)
    setSelectedOption(null)
    
    // Reset counts when category changes
    setOptionsCount(0)
    setFeaturesCount(0)
    
    if (category && activeSection === 'categories') {
      setActiveSection('services')
    }
  }, [activeSection])

  // Handle service selection with options count fetching
  const handleServiceSelect = useCallback((service: Service | null) => {
    setSelectedService(service)
    setSelectedOption(null)
    
    // Fetch options count for the selected service
    if (service) {
      fetchOptionsCount(service.id.toString())
    } else {
      setOptionsCount(0)
    }
    
    if (service && activeSection === 'services') {
      setActiveSection('options')
    }
  }, [activeSection, fetchOptionsCount])

  // Handle option selection with features count fetching
  const handleOptionSelect = useCallback((option: ServiceOption | null) => {
    setSelectedOption(option)
    
    // Fetch features count for the selected option
    if (option) {
      fetchFeaturesCount(option.id.toString())
    } else {
      setFeaturesCount(0)
    }
    
    if (option && activeSection === 'options') {
      setActiveSection('features')
    }
  }, [activeSection, fetchFeaturesCount])

  // Main Render
  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Header Component */}
      <Header
        selectedCategory={selectedCategory}
        selectedService={selectedService}
        selectedOption={selectedOption}
      />

      {/* Section Navigation */}
      <SectionNavigation
        sections={sections}
        onSectionChange={handleSectionChange}
        getCounter={getCounter}
      />

      {/* Content Area with Animated Transitions */}
      <div
        className="flex-1 rounded-lg shadow-sm border border-gray-200/50 overflow-hidden"
        role="main"
        aria-label={`${sections.find(s => s.isActive)?.title} management section`}
      >
        <AnimatePresence mode="wait">
          {activeSection === 'categories' && (
            <div
              key="categories"
              data-section="categories"
            >
              <CategoryManagement
                selectedCategory={selectedCategory}
                onCategorySelect={handleCategorySelect}
                showSuccess={showSuccess}
                showError={showError}
                showWarning={showWarning}
                showInfo={showInfo}
                showLoading={showLoading}
                clearLoadingNotifications={clearLoadingNotifications}
              />
            </div>
          )}

          {activeSection === 'services' && selectedCategory && (
            <div
              key="services"
              data-section="services"
            >
              <ServiceManagementComponent
                category={selectedCategory}
                selectedService={selectedService}
                onServiceSelect={handleServiceSelect}
                showSuccess={showSuccess}
                showError={showError}
                showWarning={showWarning}
                showInfo={showInfo}
                showLoading={showLoading}
                clearLoadingNotifications={clearLoadingNotifications}
              />
            </div>
          )}

          {activeSection === 'options' && selectedService && (
            <div
              key="options"
              data-section="options"
            >
              <ServiceOptionsManagement
                service={selectedService}
                selectedOption={selectedOption}
                onOptionSelect={handleOptionSelect}
                showSuccess={showSuccess}
                showError={showError}
                showWarning={showWarning}
                showInfo={showInfo}
                showLoading={showLoading}
                clearLoadingNotifications={clearLoadingNotifications}
              />
            </div>
          )}

          {activeSection === 'features' && selectedOption && (
            <div
              key="features"
              data-section="features"
            >
              <OptionFeaturesManagement
                option={selectedOption}
                showSuccess={showSuccess}
                showError={showError}
                showWarning={showWarning}
                showInfo={showInfo}
                showLoading={showLoading}
                clearLoadingNotifications={clearLoadingNotifications}
              />
            </div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}
