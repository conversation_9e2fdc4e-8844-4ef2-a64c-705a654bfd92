'use client'

// Imports
import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { motion } from 'framer-motion'
import { PencilIcon, XMarkIcon } from '@heroicons/react/24/outline'

// Type definitions for category data structure
interface Category {
  id: string
  name: string
  description?: string
  parentId?: string // Optional parent for hierarchical categories
  isActive: boolean
  displayOrder: number // Controls sorting order in lists
}

interface CategoryFormModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => Promise<void>
  editingCategory?: Category | null // Null for create mode, Category for edit mode
  categories: Category[] // All categories for parent selection
  title?: string
  subtitle?: string
}

interface FormData {
  name: string
  description: string
  parentId: string // Empty string means root category
  isActive: boolean
  displayOrder: number
}

// Modal configuration constants - centralized for easy maintenance
const MODAL_CONFIG = {
  width: 800,
  initialHeight: 600,
  headerHeight: 80,
  footerHeight: 60,
  padding: 64,
  maxViewportRatio: 0.9 // Maximum 90% of viewport height
}

// Main Component
export const CategoryFormModal = React.memo<CategoryFormModalProps>(({ 
  isOpen, 
  onClose, 
  onSubmit, 
  editingCategory, 
  categories,
  title,
  subtitle 
}) => {
  // State Management
  // Modal positioning and sizing state
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [size, setSize] = useState<{ width: number; height: number }>({ 
    width: MODAL_CONFIG.width, 
    height: MODAL_CONFIG.initialHeight 
  })
  const [isReady, setIsReady] = useState(false) // Prevents flash during positioning
  
  // Drag and resize interaction state
  const [isDragging, setIsDragging] = useState(false)
  const [isResizing, setIsResizing] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [resizeStart, setResizeStart] = useState({ x: 0, y: 0, width: 0, height: 0 })
  
  // DOM references for modal manipulation
  const elementRef = useRef<HTMLDivElement>(null)
  const actualContentRef = useRef<HTMLDivElement>(null)

  // Computed Values
  // Memoized initial form data - prevents unnecessary recalculations
  const initialFormData: FormData = useMemo(() => ({
    name: editingCategory?.name || '',
    description: editingCategory?.description || '',
    parentId: editingCategory?.parentId || '', // Empty string = root category
    isActive: editingCategory?.isActive ?? true, // Default to true for new categories
    displayOrder: editingCategory?.displayOrder || 0
  }), [editingCategory])

  const [formData, setFormData] = useState<FormData>(initialFormData)

  // Effects
  // Update form data when editing category changes (create vs edit mode)
  useEffect(() => {
    setFormData(initialFormData)
  }, [initialFormData])

  // Utility Functions
  // Calculate modal position with viewport boundary constraints
  const calculateModalPosition = useCallback((width: number, height: number) => {
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    const isMobile = viewportWidth < 768
    
    if (isMobile) {
      // On mobile, center the modal with padding
      const left = Math.max(16, (viewportWidth - width) / 2)
      const top = Math.max(16, (viewportHeight - height) / 2)
      return { x: left, y: top }
    }
    
    // Desktop: Center calculation with minimum bounds
    const left = Math.max(0, (viewportWidth - width) / 2)
    const top = Math.max(0, (viewportHeight - height) / 2)
    
    // Ensure modal stays within viewport bounds
    return {
      x: Math.min(left, viewportWidth - width),
      y: Math.min(top, viewportHeight - height)
    }
  }, [])

  // Auto-height calculation based on content size
  const calculateAutoHeight = useCallback(() => {
    if (!actualContentRef.current) return

    const contentHeight = actualContentRef.current.scrollHeight
    const viewportHeight = window.innerHeight
    const totalHeight = contentHeight + MODAL_CONFIG.headerHeight + MODAL_CONFIG.footerHeight + MODAL_CONFIG.padding
    const maxHeight = viewportHeight * MODAL_CONFIG.maxViewportRatio
    const newHeight = Math.min(maxHeight, totalHeight)
    
    // Re-center modal with new height
    const newPosition = calculateModalPosition(MODAL_CONFIG.width, newHeight)
    
    setSize(prev => ({ ...prev, height: newHeight }))
    setPosition(newPosition)
  }, [calculateModalPosition])

  // Modal positioning and auto-height calculation
  useEffect(() => {
    if (!isOpen) {
      setIsReady(false)
      return
    }

    setIsReady(false) // Hide modal while calculating to prevent flash
    
    // Responsive sizing based on screen width
    const isMobile = window.innerWidth < 768
    const isTablet = window.innerWidth < 1024
    
    let modalWidth = MODAL_CONFIG.width
    let modalHeight = MODAL_CONFIG.initialHeight
    
    if (isMobile) {
      modalWidth = Math.min(window.innerWidth - 32, 400) // Full width minus padding
      modalHeight = Math.min(window.innerHeight - 32, 600) // Full height minus padding
    } else if (isTablet) {
      modalWidth = Math.min(window.innerWidth - 64, 600) // Tablet width
      modalHeight = MODAL_CONFIG.initialHeight // Keep default height for tablet
    }
    
    // Set initial position and size
    const initialPosition = calculateModalPosition(modalWidth, modalHeight)
    setSize({ width: modalWidth, height: modalHeight })
    setPosition(initialPosition)
    
    // Show modal and calculate optimal height
    const showModal = () => {
      setIsReady(true)
      setTimeout(calculateAutoHeight, 50) // Small delay to ensure content is rendered
    }
    
    setTimeout(showModal, 10)
  }, [isOpen, calculateModalPosition, calculateAutoHeight])

  // Event Handlers
  // Handle border resize initiation - stores direction for multi-directional resizing
  const handleBorderResizeMouseDown = useCallback((e: React.MouseEvent, direction: string) => {
    e.preventDefault()
    e.stopPropagation()
    setIsResizing(true)
    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: size.width,
      height: size.height
    })
    // Store resize direction in DOM attribute for mouse move handler
    e.currentTarget.setAttribute('data-resize-direction', direction)
  }, [size.width, size.height])

  // Handle drag initiation - calculates offset for smooth dragging
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
    setDragStart({
      x: e.clientX - position.x, // Store offset from mouse to modal corner
      y: e.clientY - position.y
    })
  }, [position.x, position.y])

  // Complex mouse move handler for both dragging and resizing
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!elementRef.current) return

    if (isDragging) {
      const newX = e.clientX - dragStart.x
      const newY = e.clientY - dragStart.y
      
      // Constrain modal within viewport bounds during drag
      const maxX = window.innerWidth - size.width
      const maxY = window.innerHeight - size.height
      
      const constrainedX = Math.max(0, Math.min(maxX, newX))
      const constrainedY = Math.max(0, Math.min(maxY, newY))
      
      // Direct DOM manipulation for smooth dragging (no React re-renders)
      elementRef.current.style.left = `${constrainedX}px`
      elementRef.current.style.top = `${constrainedY}px`
      elementRef.current.style.transform = 'none'
      elementRef.current.style.transition = 'none'
    } else if (isResizing) {
      const deltaX = e.clientX - resizeStart.x
      const deltaY = e.clientY - resizeStart.y
      
      // Get resize direction from DOM attribute
      const resizeElement = document.querySelector('[data-resize-direction]')
      const direction = resizeElement?.getAttribute('data-resize-direction') || 'se'
      
      let newWidth = resizeStart.width
      let newHeight = resizeStart.height
      
      // Handle multi-directional resizing (north, south, east, west)
      if (direction.includes('e')) { // East (right edge)
        newWidth = Math.max(200, Math.min(window.innerWidth, resizeStart.width + deltaX))
      }
      if (direction.includes('w')) { // West (left edge)
        newWidth = Math.max(200, Math.min(window.innerWidth, resizeStart.width - deltaX))
      }
      if (direction.includes('s')) { // South (bottom edge)
        newHeight = Math.max(1, Math.min(window.innerHeight, resizeStart.height + deltaY))
      }
      if (direction.includes('n')) { // North (top edge)
        newHeight = Math.max(1, Math.min(window.innerHeight, resizeStart.height - deltaY))
      }
      
      // Direct DOM manipulation for smooth resizing
      elementRef.current.style.width = `${newWidth}px`
      elementRef.current.style.height = `${newHeight}px`
      elementRef.current.style.transition = 'none'
    }
  }, [isDragging, isResizing, dragStart, resizeStart, size.width, size.height])

  // Clean up drag/resize state and sync with React state
  const handleMouseUp = useCallback(() => {
    if (!elementRef.current) return

    // Sync React state with final DOM position for consistency
    if (isDragging || isResizing) {
      const rect = elementRef.current.getBoundingClientRect()
      setPosition({ x: rect.left, y: rect.top })
      setSize({ width: rect.width, height: rect.height })
    }
    
    // Restore CSS transitions when interaction ends
    elementRef.current.style.transition = 'box-shadow 0.2s ease'
    
    // Clean up resize direction attribute
    const resizeElement = document.querySelector('[data-resize-direction]')
    resizeElement?.removeAttribute('data-resize-direction')
    
    setIsDragging(false)
    setIsResizing(false)
  }, [isDragging, isResizing])

  // Global event listeners for drag/resize operations
  useEffect(() => {
    if (!isDragging && !isResizing) return

    const options = { passive: true } // Optimize performance
    document.addEventListener('mousemove', handleMouseMove, options)
    document.addEventListener('mouseup', handleMouseUp, options)
    document.addEventListener('mouseleave', handleMouseUp, options) // Handle mouse leaving window
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.removeEventListener('mouseleave', handleMouseUp)
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp])

  // Handle click outside modal to close
  useEffect(() => {
    if (!isOpen) return

    const handleClickOutside = (event: MouseEvent) => {
      if (elementRef.current && !elementRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [isOpen, onClose])

  // Handle window resize - keep modal within viewport
  useEffect(() => {
    const handleWindowResize = () => {
      if (!elementRef.current) return

      const rect = elementRef.current.getBoundingClientRect()
      const maxX = window.innerWidth - size.width
      const maxY = window.innerHeight - size.height
      
      // Adjust position if modal is now outside viewport
      if (rect.right > window.innerWidth || rect.bottom > window.innerHeight) {
        const newX = Math.max(0, Math.min(maxX, position.x))
        const newY = Math.max(0, Math.min(maxY, position.y))
        setPosition({ x: newX, y: newY })
      }
    }

    window.addEventListener('resize', handleWindowResize)
    return () => window.removeEventListener('resize', handleWindowResize)
  }, [position, size])

  // Form submission handler
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      await onSubmit(formData)
      onClose()
    } catch (error) {
      console.error('Error submitting form:', error)
      // Note: Error handling is delegated to parent component
    }
  }, [formData, onSubmit, onClose])

  // Optimized form data updater - prevents unnecessary re-renders
  const updateFormData = useCallback((field: keyof FormData, value: string | boolean | number) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }, [])

  // Filter out current category from parent options to prevent circular references
  const filteredCategories = useMemo(() => 
    categories.filter(cat => !editingCategory || cat.id !== editingCategory.id),
    [categories, editingCategory]
  )

  // Memoized modal title and subtitle for performance
  const modalTitle = useMemo(() => 
    title || (editingCategory ? 'Edit Category' : 'New Category'),
    [title, editingCategory]
  )

  const modalSubtitle = useMemo(() => 
    subtitle || 'Category management',
    [subtitle]
  )

  // Early return to prevent rendering during positioning calculations
  if (!isOpen || !isReady) return null

  // Rendering
  return (
    <>
      {/* CSS animation keyframes for modal appearance */}
      <style jsx>{`
        @keyframes modalAppear {
          0% {
            opacity: 0;
            transform: scale(0.9) translateY(-20px);
          }
          100% {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }
      `}</style>
      
      {/* Modal Backdrop/Overlay */}
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.1)',
          zIndex: 99998,
          backdropFilter: 'blur(2px)', // Subtle blur effect
        }}
        onClick={onClose}
      />
      
      {/* Draggable and Resizable Modal Window */}
      <motion.div
        ref={elementRef}
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        style={{
          position: 'fixed',
          zIndex: 99999,
          backgroundColor: 'white',
          borderRadius: window.innerWidth < 768 ? '8px' : '12px',
          // Dynamic shadow based on interaction state
          boxShadow: isDragging ? '0 30px 60px -12px rgba(0, 0, 0, 0.5)' : 
                     isResizing ? '0 25px 50px -12px rgba(0, 0, 0, 0.4)' : 
                     '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          overflow: 'hidden',
          cursor: isResizing ? 'nw-resize' : 'default',
          padding: 0,
          margin: 0,
          width: `${size.width}px`,
          height: `${size.height}px`,
          left: `${position.x}px`,
          top: `${position.y}px`,
          isolation: 'isolate', // CSS containment for performance
          contain: 'layout style paint',
          resize: 'none',
          // Disable transitions during drag/resize for smooth interaction
          transition: isDragging || isResizing ? 'none' : 'box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
          animation: 'modalAppear 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)',
          // Mobile-specific styles
          maxWidth: window.innerWidth < 768 ? '100vw' : 'none',
          maxHeight: window.innerWidth < 768 ? '100vh' : 'none',
        }}
        onClick={(e) => e.stopPropagation()} // Prevent backdrop click
      >
        {/* Draggable Header */}
        <div
          style={{
            background: 'linear-gradient(to right, #3b82f6, #2563eb)',
            borderBottom: '2px solid #1d4ed8',
            padding: window.innerWidth < 768 ? '12px 16px' : '16px 24px',
            display: 'flex',
            alignItems: 'center',
            gap: window.innerWidth < 768 ? '12px' : '16px',
            marginBottom: '-8px',
            cursor: isDragging ? 'grabbing' : 'move',
            userSelect: 'none', // Prevent text selection during drag
            WebkitUserSelect: 'none',
            MozUserSelect: 'none',
            msUserSelect: 'none',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            opacity: 1,
            transform: 'none',
            backgroundColor: 'transparent',
          }}
          onMouseDown={handleMouseDown}
        >
          {/* Icon Container */}
          <div
            style={{
              padding: '0px',
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <PencilIcon className={`${window.innerWidth < 768 ? 'h-5 w-5' : 'h-6 w-6'} text-white`} />
          </div>

          {/* Text Container */}
          <div
            style={{
              flex: 1,
              backgroundColor: 'transparent',
              background: 'none',
              backgroundImage: 'none',
              backgroundClip: 'unset',
              WebkitBackgroundClip: 'unset',
            }}
          >
            <h3
              style={{
                fontSize: window.innerWidth < 768 ? '18px' : '20px',
                fontWeight: '600',
                color: 'white',
                margin: 0,
                backgroundColor: 'transparent',
                background: 'none',
                backgroundImage: 'none',
                backgroundClip: 'unset',
                WebkitBackgroundClip: 'unset',
                WebkitTextFillColor: 'white',
                textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
              }}
            >
              {modalTitle}
            </h3>
            <p
              style={{
                fontSize: window.innerWidth < 768 ? '12px' : '14px',
                fontWeight: '400',
                color: 'rgba(255, 255, 255, 0.9)',
                margin: 0,
                backgroundColor: 'transparent',
                background: 'none',
                backgroundImage: 'none',
                backgroundClip: 'unset',
                WebkitBackgroundClip: 'unset',
                WebkitTextFillColor: 'rgba(255, 255, 255, 0.9)',
              }}
            >
              {modalSubtitle}
            </p>
          </div>

          {/* Close Button with Hover Effects */}
          <button
            onClick={onClose}
            style={{
              padding: window.innerWidth < 768 ? '6px' : '8px',
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.2)'
              e.currentTarget.style.transform = 'scale(1.05)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)'
              e.currentTarget.style.transform = 'scale(1)'
            }}
          >
            <XMarkIcon className={`${window.innerWidth < 768 ? 'h-4 w-4' : 'h-5 w-5'} text-white`} />
          </button>
        </div>

        {/* Scrollable Content Area */}
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            height: 'calc(100% - 60px)', // Account for header height
            paddingBottom: '60px', // Account for footer height
            opacity: 1,
            transform: 'none',
            backgroundColor: 'transparent',
            flex: 1,
          }}
        >
          <div
            className="modal-content"
            style={{
              flex: 1,
              padding: window.innerWidth < 768 ? '16px' : '32px',
              cursor: 'default',
              overflowY: 'auto', // Enable scrolling for long content
              background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
            }}
          >
            <div ref={actualContentRef} key="content-wrapper" style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
              {/* Category Information Form Section */}
              <div
                key="category-info"
                style={{
                  backgroundColor: 'white',
                  borderRadius: window.innerWidth < 768 ? '8px' : '12px',
                  padding: window.innerWidth < 768 ? '16px' : '24px',
                  boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
                  border: '1px solid #e2e8f0',
                }}
              >
                <h3 style={{
                  fontSize: '18px',
                  fontWeight: '600',
                  color: '#1e293b',
                  margin: '0 0 20px 0',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                }}>
                  <PencilIcon className="h-4 w-4 text-blue-600" />
                  Category Information
                </h3>
                
                <form onSubmit={handleSubmit}>
                  <div style={{ 
                    display: 'grid', 
                    gridTemplateColumns: window.innerWidth < 768 ? '1fr' : '1fr 1fr', 
                    gap: window.innerWidth < 768 ? '12px' : '16px' 
                  }}>
                    {/* Name Field */}
                    <div>
                      <label style={{
                        display: 'block',
                        fontSize: '14px',
                        fontWeight: '500',
                        color: '#374151',
                        marginBottom: '6px',
                      }}>
                        Name <span style={{ color: '#ef4444' }}>*</span>
                      </label>
                      <input
                        type="text"
                        required
                        maxLength={50} // Business rule: 50 character limit
                        value={formData.name}
                        onChange={(e) => updateFormData('name', e.target.value)}
                        placeholder="Category name"
                        style={{
                          width: '100%',
                          padding: '10px 12px',
                          border: '1px solid #d1d5db',
                          borderRadius: '6px',
                          fontSize: '14px',
                          backgroundColor: 'white',
                          transition: 'border-color 0.2s ease',
                        }}
                        onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                        onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                      />
                      <p style={{ fontSize: '12px', color: '#6b7280', margin: '4px 0 0 0' }}>
                        {formData.name.length}/50
                      </p>
                    </div>

                    {/* Parent Category */}
                    <div>
                      <label style={{
                        display: 'block',
                        fontSize: '14px',
                        fontWeight: '500',
                        color: '#374151',
                        marginBottom: '6px',
                      }}>Parent Category</label>
                      <select
                        value={formData.parentId}
                        onChange={(e) => updateFormData('parentId', e.target.value)}
                        style={{
                          width: '100%',
                          padding: '10px 12px',
                          border: '1px solid #d1d5db',
                          borderRadius: '6px',
                          fontSize: '14px',
                          backgroundColor: 'white',
                          transition: 'border-color 0.2s ease',
                        }}
                        onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                        onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                      >
                        <option value="">No parent (root category)</option>
                        {/* Filtered categories prevent circular references */}
                        {filteredCategories.map((cat) => (
                          <option key={cat.id} value={cat.id}>
                            {cat.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Description Field */}
                  <div style={{ marginTop: '16px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '6px',
                    }}>Description</label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => updateFormData('description', e.target.value)}
                      placeholder="Brief description"
                      rows={2}
                      style={{
                        width: '100%',
                        padding: '10px 12px',
                        border: '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px',
                        backgroundColor: 'white',
                        resize: 'vertical', // Allow vertical resize only
                        fontFamily: 'inherit',
                        transition: 'border-color 0.2s ease',
                      }}
                      onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                      onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                    />
                  </div>

                  {/* Settings Row */}
                  <div style={{ 
                    display: 'grid', 
                    gridTemplateColumns: window.innerWidth < 768 ? '1fr' : '1fr 1fr', 
                    gap: window.innerWidth < 768 ? '12px' : '16px', 
                    marginTop: window.innerWidth < 768 ? '12px' : '16px' 
                  }}>
                    <div>
                      <label style={{
                        display: 'block',
                        fontSize: '14px',
                        fontWeight: '500',
                        color: '#374151',
                        marginBottom: '6px',
                      }}>Display Order</label>
                      <input
                        type="number"
                        min="0"
                        value={formData.displayOrder}
                        onChange={(e) => updateFormData('displayOrder', Number(e.target.value) || 0)}
                        style={{
                          width: '80px',
                          padding: '10px 12px',
                          border: '1px solid #d1d5db',
                          borderRadius: '6px',
                          fontSize: '14px',
                          backgroundColor: 'white',
                          transition: 'border-color 0.2s ease',
                        }}
                        onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                        onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                      />
                    </div>

                    <div>
                      <label style={{
                        display: 'block',
                        fontSize: '14px',
                        fontWeight: '500',
                        color: '#374151',
                        marginBottom: '6px',
                      }}>Status</label>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                        <input
                          type="checkbox"
                          checked={formData.isActive}
                          onChange={(e) => updateFormData('isActive', e.target.checked)}
                          style={{ margin: 0 }}
                        />
                        <span style={{ fontSize: '14px', color: '#374151' }}>
                          {formData.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        {/* Fixed Footer with Action Buttons */}
        <div
          style={{
            backgroundColor: 'white',
            padding: window.innerWidth < 768 ? '16px 16px 0 16px' : '24px 24px 0 24px',
            borderTop: '1px solid #e2e8f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            minHeight: window.innerWidth < 768 ? '50px' : '60px',
            opacity: 1,
            transform: 'none',
            borderBottomLeftRadius: window.innerWidth < 768 ? '8px' : '12px',
            borderBottomRightRadius: window.innerWidth < 768 ? '8px' : '12px',
          }}
        >
          {/* Centered Action Buttons */}
          <div style={{
            display: 'flex',
            gap: window.innerWidth < 768 ? '8px' : '12px',
            position: 'absolute',
            left: '50%',
            top: window.innerWidth < 768 ? '8px' : '12px',
            transform: 'translateX(-50%)',
          }}>
            <button
              type="button"
              onClick={onClose}
              style={{
                padding: window.innerWidth < 768 ? '10px 16px' : '12px 24px',
                backgroundColor: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: window.innerWidth < 768 ? '13px' : '14px',
                fontWeight: '600',
                transition: 'all 0.2s ease',
                boxShadow: '0 2px 4px rgba(107, 114, 128, 0.3)',
                transform: 'translateY(-2px)',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#4b5563'
                e.currentTarget.style.transform = 'translateY(-3px)'
                e.currentTarget.style.boxShadow = '0 4px 8px rgba(107, 114, 128, 0.4)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = '#6b7280'
                e.currentTarget.style.transform = 'translateY(-2px)'
                e.currentTarget.style.boxShadow = '0 2px 4px rgba(107, 114, 128, 0.3)'
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              onClick={handleSubmit}
              style={{
                padding: window.innerWidth < 768 ? '10px 16px' : '12px 24px',
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: window.innerWidth < 768 ? '13px' : '14px',
                fontWeight: '600',
                transition: 'all 0.2s ease',
                boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)',
                transform: 'translateY(-2px)',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#2563eb'
                e.currentTarget.style.transform = 'translateY(-3px)'
                e.currentTarget.style.boxShadow = '0 4px 8px rgba(59, 130, 246, 0.4)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = '#3b82f6'
                e.currentTarget.style.transform = 'translateY(-2px)'
                e.currentTarget.style.boxShadow = '0 2px 4px rgba(59, 130, 246, 0.3)'
              }}
            >
              {editingCategory ? 'Update' : 'Create'}
            </button>
          </div>
        </div>

        {/* Multi-directional Resize Handles - Hidden on mobile */}
        {window.innerWidth >= 768 && ['n', 'e', 's', 'w'].map((direction) => (
          <div
            key={direction}
            style={{
              position: 'absolute',
              top: direction === 'n' ? 0 : direction === 's' ? 'auto' : 0,
              right: direction === 'e' ? 0 : direction === 'w' ? 'auto' : 0,
              bottom: direction === 's' ? 0 : direction === 'n' ? 'auto' : 0,
              left: direction === 'w' ? 0 : direction === 'e' ? 'auto' : 0,
              width: direction === 'e' || direction === 'w' ? '4px' : '100%',
              height: direction === 'n' || direction === 's' ? '4px' : '100%',
              cursor: `${direction}-resize`,
              zIndex: 1000,
            }}
            onMouseDown={(e) => handleBorderResizeMouseDown(e, direction)}
          />
        ))}
      </motion.div>
    </>
  )
})

CategoryFormModal.displayName = 'CategoryFormModal'
