import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    console.log('Blog API: Starting request');
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const sortBy = searchParams.get('sortBy') || 'updatedat';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const limit = parseInt(searchParams.get('limit') || '100');
    const page = parseInt(searchParams.get('page') || '1');
    const offset = (page - 1) * limit;

    console.log('Blog API: Parameters:', { search, sortBy, sortOrder, limit, page, offset });

    // Build where clause for search
    const whereClause = search ? {
      OR: [
        { title: { contains: search, mode: 'insensitive' as const } },
        { content: { contains: search, mode: 'insensitive' as const } },
        { excerpt: { contains: search, mode: 'insensitive' as const } },
        { categories: { contains: search, mode: 'insensitive' as const } },
        { tags: { contains: search, mode: 'insensitive' as const } }
      ]
    } : {};

    // Get total count
    console.log('Blog API: Getting total count with whereClause:', whereClause);
    const totalCount = await prisma.blogposts.count({
      where: whereClause
    });
    console.log('Blog API: Total count:', totalCount);

    // Get blog posts with pagination
    console.log('Blog API: Getting blog posts');
    const blogPosts = await prisma.blogposts.findMany({
      where: whereClause,
      orderBy: {
        [sortBy]: sortOrder as 'asc' | 'desc'
      },
      skip: offset,
      take: limit
    });
    console.log('Blog API: Found blog posts:', blogPosts.length);

    // Transform data to match the expected format
    const transformedPosts = blogPosts.map(post => ({
      id: post.id.toString(),
      title: post.title,
      slug: post.slug,
      content: post.content,
      excerpt: post.excerpt || '',
      featuredImageUrl: post.featuredimageurl || '',
      authorId: post.authorid || '',
      isPublished: post.ispublished || false,
      publishedAt: post.publishedat?.toISOString() || '',
      categories: post.categories || '',
      tags: post.tags || '',
      createdAt: post.createdat.toISOString(),
      updatedAt: post.updatedat?.toISOString() || post.createdat.toISOString()
    }));

    return NextResponse.json({
      success: true,
      posts: transformedPosts,
      totalCount: totalCount,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      }
    });

  } catch (error) {
    console.error('Blog API: Error fetching blog posts:', error);
    console.error('Blog API: Error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace'
    });
    return NextResponse.json(
      { success: false, error: 'Failed to fetch blog posts' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const blogPost = await prisma.blogposts.create({
      data: {
        title: body.title,
        slug: body.slug,
        content: body.content,
        excerpt: body.excerpt || '',
        featuredimageurl: body.featuredImageUrl || '',
        authorid: body.authorId || '',
        ispublished: body.isPublished || false,
        publishedat: body.publishedAt ? new Date(body.publishedAt) : null,
        categories: body.categories || '',
        tags: body.tags || ''
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        id: blogPost.id.toString(),
        title: blogPost.title,
        slug: blogPost.slug,
        content: blogPost.content,
        excerpt: blogPost.excerpt || '',
        featuredImageUrl: blogPost.featuredimageurl || '',
        authorId: blogPost.authorid || '',
        isPublished: blogPost.ispublished || false,
        publishedAt: blogPost.publishedat?.toISOString() || '',
        categories: blogPost.categories || '',
        tags: blogPost.tags || '',
        createdAt: blogPost.createdat.toISOString(),
        updatedAt: blogPost.updatedat?.toISOString() || blogPost.createdat.toISOString()
      }
    });

  } catch (error) {
    console.error('Error creating blog post:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create blog post' },
      { status: 500 }
    );
  }
}