import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const blogPost = await prisma.blogposts.findUnique({
      where: { id: BigInt(params.id) }
    });

    if (!blogPost) {
      return NextResponse.json(
        { success: false, error: 'Blog post not found' },
        { status: 404 }
      );
    }

    const transformedPost = {
      id: blogPost.id.toString(),
      title: blogPost.title,
      slug: blogPost.slug,
      content: blogPost.content,
      excerpt: blogPost.excerpt || '',
      featuredImageUrl: blogPost.featuredimageurl || '',
      authorId: blogPost.authorid || '',
      isPublished: blogPost.ispublished || false,
      publishedAt: blogPost.publishedat?.toISOString() || '',
      categories: blogPost.categories || '',
      tags: blogPost.tags || '',
      createdAt: blogPost.createdat.toISOString(),
      updatedAt: blogPost.updatedat?.toISOString() || blogPost.createdat.toISOString()
    };

    return NextResponse.json({
      success: true,
      data: transformedPost
    });

  } catch (error) {
    console.error('Error fetching blog post:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch blog post' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    const blogPost = await prisma.blogposts.update({
      where: { id: BigInt(params.id) },
      data: {
        title: body.title,
        slug: body.slug,
        content: body.content,
        excerpt: body.excerpt || '',
        featuredimageurl: body.featuredImageUrl || '',
        authorid: body.authorId || '',
        ispublished: body.isPublished || false,
        publishedat: body.publishedAt ? new Date(body.publishedAt) : null,
        categories: body.categories || '',
        tags: body.tags || '',
        updatedat: new Date()
      }
    });

    const transformedPost = {
      id: blogPost.id.toString(),
      title: blogPost.title,
      slug: blogPost.slug,
      content: blogPost.content,
      excerpt: blogPost.excerpt || '',
      featuredImageUrl: blogPost.featuredimageurl || '',
      authorId: blogPost.authorid || '',
      isPublished: blogPost.ispublished || false,
      publishedAt: blogPost.publishedat?.toISOString() || '',
      categories: blogPost.categories || '',
      tags: blogPost.tags || '',
      createdAt: blogPost.createdat.toISOString(),
      updatedAt: blogPost.updatedat?.toISOString() || blogPost.createdat.toISOString()
    };

    return NextResponse.json({
      success: true,
      data: transformedPost
    });

  } catch (error) {
    console.error('Error updating blog post:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update blog post' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await prisma.blogposts.delete({
      where: { id: BigInt(params.id) }
    });

    return NextResponse.json({
      success: true,
      message: 'Blog post deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting blog post:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete blog post' },
      { status: 500 }
    );
  }
}