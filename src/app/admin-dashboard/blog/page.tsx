'use client';

import { BlogsManagement } from '@/components/admin/blog/blogs-management';
import { ReactNode } from 'react';

// Inline type definitions (extracted from crud/types.ts)
interface CrudColumn<T = any> {
  key: keyof T | string
  label: string
  sortable?: boolean
  searchable?: boolean
  renderType?: 'text' | 'email' | 'date' | 'currency' | 'boolean' | 'status' | 'rating' | 'progress' | 'image' | 'custom' | 'company' | 'number'
  renderFunction?: (value: any, row: T) => React.ReactNode
  renderProps?: any
  width?: string
  className?: string
  hideable?: boolean
  defaultVisible?: boolean
}

interface CrudField {
  key: string
  label: string
  name?: string
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'boolean' | 'radio' | 'date' | 'datetime-local' | 'url' | 'file'
  required?: boolean
  searchable?: boolean
  placeholder?: string
  defaultValue?: any
  options?: Array<{ value: string; label: string }>
  validation?: {
    minLength?: number
    maxLength?: number
    min?: number
    max?: number
    pattern?: RegExp
    step?: number
  }
  rows?: number
  multiple?: boolean
  accept?: string
}

interface CrudFilter {
  key: string
  label: string
  name?: string
  type: 'text' | 'select' | 'date' | 'daterange' | 'checkbox'
  options?: Array<{ value: string; label: string }>
  placeholder?: string
}

interface FormSection {
  title: string
  fields: string[]
}

interface FormLayout {
  type: 'compact' | 'full'
  columns: number
  sections: FormSection[]
}

interface CrudAction<T = any> {
  label: string
  icon: string
  action: 'edit' | 'delete' | 'view' | 'preview' | 'toggle-status' | 'toggle-published' | 'toggle-featured' | 'custom'
  customAction?: string
  variant?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning'
  disabled?: boolean
  hidden?: boolean
  requiresConfirmation?: boolean
  confirmationMessage?: string
  tooltip?: string
}

interface CrudBulkAction<T = any> {
  label: string
  icon?: string
  action: string
  variant?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning'
  requiresConfirmation?: boolean
  confirmationMessage?: string
}

interface CrudPermissions {
  create: boolean
  read: boolean
  update: boolean
  delete: boolean
  export: boolean
}

type ViewMode = 'list' | 'grid'
type DisplayDensity = 'compact' | 'comfortable'

interface ViewSettings {
  mode: ViewMode
  density: DisplayDensity
  visibleColumns: string[]
}

interface CrudConfig<T = any> {
  title: string
  description?: string
  endpoint: string
  columns: CrudColumn<T>[]
  fields: CrudField[]
  filters?: CrudFilter[]
  actions?: CrudAction<T>[]
  bulkActions?: CrudBulkAction<T>[]
  permissions: CrudPermissions
  searchPlaceholder?: string
  defaultSort?: { field: string; direction: 'asc' | 'desc' }
  pageSize?: number
  enableSearch?: boolean
  enableFilters?: boolean
  enableBulkActions?: boolean
  enableExport?: boolean
  enableViewControls?: boolean
  enableDensityControls?: boolean
  enableColumnVisibility?: boolean
  defaultViewSettings?: Partial<ViewSettings>
  formLayout?: FormLayout
  customCreateButton?: ReactNode
  customHeader?: ReactNode
  customFooter?: ReactNode
  onItemClick?: boolean
}

interface BlogPost {
  id: string
  title: string
  slug: string
  content: string
  excerpt?: string
  featuredImageUrl?: string
  authorId?: string
  isPublished: boolean
  publishedAt?: string
  categories?: string
  tags?: string
  createdAt: string
  updatedAt: string
  _count?: {
    comments?: number;
    views?: number;
    [key: string]: number | undefined;
  }
}

const blogConfig: CrudConfig<BlogPost> = {
  title: 'Blog Posts',
  description: 'Create, edit, and manage your blog content',
  endpoint: 'blog', // API endpoint

  // Table columns configuration - Most important blog columns
  columns: [
    {
      key: 'title',
      label: 'Title',
      sortable: true,
      searchable: true,
      width: '25%'
    },
    {
      key: 'excerpt',
      label: 'Excerpt',
      sortable: false,
      searchable: true,
      width: '20%'
    },
    {
      key: 'isPublished',
      label: 'Status',
      sortable: true,
      searchable: false,
      width: '10%'
    },
    {
      key: 'categories',
      label: 'Categories',
      sortable: true,
      searchable: true,
      width: '15%'
    },
    {
      key: 'tags',
      label: 'Tags',
      sortable: false,
      searchable: true,
      width: '15%'
    },
    {
      key: 'publishedAt',
      label: 'Published',
      sortable: true,
      searchable: false,
      width: '15%'
    }
  ],

  // Filters configuration
  filters: [
    {
      key: 'isPublished',
      label: 'Publication Status',
      type: 'select',
      options: [
        { value: '', label: 'All Posts' },
        { value: 'true', label: 'Published' },
        { value: 'false', label: 'Draft' }
      ]
    },
    {
      key: 'categories',
      label: 'Category',
      type: 'select',
      options: [
        { value: '', label: 'All Categories' },
        { value: 'Web Development', label: 'Web Development' },
        { value: 'Mobile Development', label: 'Mobile Development' },
        { value: 'Cloud Computing', label: 'Cloud Computing' },
        { value: 'Programming', label: 'Programming' },
        { value: 'Security', label: 'Security' },
        { value: 'AI/ML', label: 'AI/ML' }
      ]
    }
  ],

  // Bulk actions configuration
  bulkActions: [
    {
      action: 'publish',
      label: 'Publish',
      icon: 'PowerIcon',
      variant: 'primary',
      confirmationMessage: 'Are you sure you want to publish the selected blog posts?'
    },
    {
      action: 'unpublish',
      label: 'Unpublish',
      icon: 'PowerIcon',
      variant: 'secondary',
      confirmationMessage: 'Are you sure you want to unpublish the selected blog posts?'
    },
    {
      action: 'delete',
      label: 'Delete',
      icon: 'TrashIcon',
      variant: 'danger',
      confirmationMessage: 'Are you sure you want to delete the selected blog posts? This action cannot be undone.'
    }
  ],

  // Action buttons for each row
  actions: [
    {
      action: 'view',
      label: 'View',
      icon: 'EyeIcon',
      variant: 'secondary',
      tooltip: 'View blog post details'
    },
    {
      action: 'edit',
      label: 'Edit',
      icon: 'PencilIcon',
      variant: 'primary',
      tooltip: 'Edit blog post'
    },
    {
      action: 'toggle-published',
      label: 'Toggle Published',
      icon: 'PowerIcon',
      variant: 'warning',
      tooltip: 'Publish/Unpublish blog post'
    },
    {
      action: 'delete',
      label: 'Delete',
      icon: 'TrashIcon',
      variant: 'danger',
      tooltip: 'Delete blog post'
    }
  ],

  fields: [
    {
      key: 'title',
      label: 'Title',
      type: 'text',
      required: true,
      searchable: true,
      placeholder: 'e.g., How to Build Amazing Web Applications'
    },
    {
      key: 'slug',
      label: 'Slug',
      type: 'text',
      required: true,
      searchable: true,
      placeholder: 'e.g., how-to-build-amazing-web-applications'
    },
    {
      key: 'excerpt',
      label: 'Excerpt',
      type: 'textarea',
      searchable: true,
      placeholder: 'Brief description of the blog post...',
      rows: 3
    },
    {
      key: 'content',
      label: 'Content',
      type: 'textarea',
      required: true,
      searchable: true,
      placeholder: 'Write your blog post content here...',
      rows: 8
    },
    {
      key: 'featuredImageUrl',
      label: 'Featured Image',
      type: 'url',
      searchable: false,
      placeholder: 'Enter image URL or click Upload to select file'
    },
    {
      key: 'authorId',
      label: 'Author ID',
      type: 'text',
      searchable: false,
      placeholder: 'e.g., author-123'
    },
    {
      key: 'categories',
      label: 'Categories',
      type: 'text',
      searchable: true,
      placeholder: 'e.g., Web Development, Technology'
    },
    {
      key: 'tags',
      label: 'Tags',
      type: 'text',
      searchable: true,
      placeholder: 'e.g., react, nextjs, javascript, tutorial'
    },
    {
      key: 'isPublished',
      label: 'Published',
      type: 'boolean',
      defaultValue: false,
      searchable: false,
    },
    {
      key: 'publishedAt',
      label: 'Published Date',
      type: 'datetime-local',
      searchable: false,
    },
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search blog posts by title, content, excerpt, categories, tags...',
  defaultSort: { field: 'updatedAt', direction: 'desc' }, // Sort by Last Active (most recent)
  pageSize: 10,
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true,
  enableViewControls: true,
  enableDensityControls: true,
  enableColumnVisibility: true,
  defaultViewSettings: {
    mode: 'list',
    density: 'comfortable',
    visibleColumns: ['title', 'excerpt', 'isPublished', 'categories', 'tags', 'publishedAt']
  },

  // Form layout configuration
  formLayout: {
    type: 'compact',
    columns: 2,
    sections: [
      {
        title: 'Basic Information',
        fields: ['title', 'slug', 'authorId', 'publishedAt']
      },
      {
        title: 'Featured Image',
        fields: ['featuredImageUrl']
      },
      {
        title: 'Content',
        fields: ['excerpt', 'content']
      },
      {
        title: 'Categories & Tags',
        fields: ['categories', 'tags']
      },
      {
        title: 'Publishing Settings',
        fields: ['isPublished']
      }
    ]
  }
};

export default function BlogPage() {
  return (
    <div className="py-6">
      <div className="mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
        <div className="admin-page" data-section="blog">
          <BlogsManagement config={blogConfig} />
        </div>
      </div>
    </div>
  );
}
