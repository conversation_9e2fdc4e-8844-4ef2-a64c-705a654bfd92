"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/blog/page",{

/***/ "(app-pages-browser)/./src/app/admin-dashboard/blog/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/admin-dashboard/blog/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_admin_blog_blogs_management__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/admin/blog/blogs-management */ \"(app-pages-browser)/./src/components/admin/blog/blogs-management.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst blogConfig = {\n    title: 'Blog Posts',\n    description: 'Create, edit, and manage your blog content',\n    endpoint: 'blog',\n    // Table columns configuration - Most important blog columns\n    columns: [\n        {\n            key: 'title',\n            label: 'Title',\n            sortable: true,\n            searchable: true,\n            width: '25%'\n        },\n        {\n            key: 'excerpt',\n            label: 'Excerpt',\n            sortable: false,\n            searchable: true,\n            width: '20%'\n        },\n        {\n            key: 'isPublished',\n            label: 'Status',\n            sortable: true,\n            searchable: false,\n            width: '10%'\n        },\n        {\n            key: 'categories',\n            label: 'Categories',\n            sortable: true,\n            searchable: true,\n            width: '15%'\n        },\n        {\n            key: 'tags',\n            label: 'Tags',\n            sortable: false,\n            searchable: true,\n            width: '15%'\n        },\n        {\n            key: 'publishedAt',\n            label: 'Published',\n            sortable: true,\n            searchable: false,\n            width: '15%'\n        }\n    ],\n    // Filters configuration\n    filters: [\n        {\n            key: 'isPublished',\n            label: 'Publication Status',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Posts'\n                },\n                {\n                    value: 'true',\n                    label: 'Published'\n                },\n                {\n                    value: 'false',\n                    label: 'Draft'\n                }\n            ]\n        },\n        {\n            key: 'categories',\n            label: 'Category',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Categories'\n                },\n                {\n                    value: 'Web Development',\n                    label: 'Web Development'\n                },\n                {\n                    value: 'Mobile Development',\n                    label: 'Mobile Development'\n                },\n                {\n                    value: 'Cloud Computing',\n                    label: 'Cloud Computing'\n                },\n                {\n                    value: 'Programming',\n                    label: 'Programming'\n                },\n                {\n                    value: 'Security',\n                    label: 'Security'\n                },\n                {\n                    value: 'AI/ML',\n                    label: 'AI/ML'\n                }\n            ]\n        }\n    ],\n    // Bulk actions configuration\n    bulkActions: [\n        {\n            action: 'publish',\n            label: 'Publish',\n            icon: 'PowerIcon',\n            variant: 'primary',\n            confirmationMessage: 'Are you sure you want to publish the selected blog posts?'\n        },\n        {\n            action: 'unpublish',\n            label: 'Unpublish',\n            icon: 'PowerIcon',\n            variant: 'secondary',\n            confirmationMessage: 'Are you sure you want to unpublish the selected blog posts?'\n        },\n        {\n            action: 'delete',\n            label: 'Delete',\n            icon: 'TrashIcon',\n            variant: 'danger',\n            confirmationMessage: 'Are you sure you want to delete the selected blog posts? This action cannot be undone.'\n        }\n    ],\n    // Action buttons for each row\n    actions: [\n        {\n            action: 'view',\n            label: 'View',\n            icon: 'EyeIcon',\n            variant: 'secondary',\n            tooltip: 'View blog post details'\n        },\n        {\n            action: 'edit',\n            label: 'Edit',\n            icon: 'PencilIcon',\n            variant: 'primary',\n            tooltip: 'Edit blog post'\n        },\n        {\n            action: 'toggle-published',\n            label: 'Toggle Published',\n            icon: 'PowerIcon',\n            variant: 'warning',\n            tooltip: 'Publish/Unpublish blog post'\n        },\n        {\n            action: 'delete',\n            label: 'Delete',\n            icon: 'TrashIcon',\n            variant: 'danger',\n            tooltip: 'Delete blog post'\n        }\n    ],\n    fields: [\n        {\n            key: 'title',\n            label: 'Title',\n            type: 'text',\n            required: true,\n            searchable: true,\n            placeholder: 'e.g., How to Build Amazing Web Applications'\n        },\n        {\n            key: 'slug',\n            label: 'Slug',\n            type: 'text',\n            required: true,\n            searchable: true,\n            placeholder: 'e.g., how-to-build-amazing-web-applications'\n        },\n        {\n            key: 'excerpt',\n            label: 'Excerpt',\n            type: 'textarea',\n            searchable: true,\n            placeholder: 'Brief description of the blog post...',\n            rows: 3\n        },\n        {\n            key: 'content',\n            label: 'Content',\n            type: 'textarea',\n            required: true,\n            searchable: true,\n            placeholder: 'Write your blog post content here...',\n            rows: 8\n        },\n        {\n            key: 'featuredImageUrl',\n            label: 'Featured Image',\n            type: 'url',\n            searchable: false,\n            placeholder: 'Enter image URL or click Upload to select file'\n        },\n        {\n            key: 'authorId',\n            label: 'Author ID',\n            type: 'text',\n            searchable: false,\n            placeholder: 'e.g., author-123'\n        },\n        {\n            key: 'categories',\n            label: 'Categories',\n            type: 'text',\n            searchable: true,\n            placeholder: 'e.g., Web Development, Technology'\n        },\n        {\n            key: 'tags',\n            label: 'Tags',\n            type: 'text',\n            searchable: true,\n            placeholder: 'e.g., react, nextjs, javascript, tutorial'\n        },\n        {\n            key: 'isPublished',\n            label: 'Published',\n            type: 'boolean',\n            defaultValue: false,\n            searchable: false\n        },\n        {\n            key: 'publishedAt',\n            label: 'Published Date',\n            type: 'datetime-local',\n            searchable: false\n        }\n    ],\n    permissions: {\n        create: true,\n        read: true,\n        update: true,\n        delete: true,\n        export: true\n    },\n    searchPlaceholder: 'Search blog posts by title, content, excerpt, categories, tags...',\n    defaultSort: {\n        field: 'updatedAt',\n        direction: 'desc'\n    },\n    pageSize: 10,\n    enableSearch: true,\n    enableFilters: true,\n    enableBulkActions: true,\n    enableExport: true,\n    enableViewControls: true,\n    enableDensityControls: true,\n    enableColumnVisibility: true,\n    defaultViewSettings: {\n        mode: 'list',\n        density: 'comfortable',\n        visibleColumns: [\n            'title',\n            'excerpt',\n            'isPublished',\n            'categories',\n            'tags',\n            'publishedAt'\n        ]\n    },\n    // Form layout configuration\n    formLayout: {\n        type: 'compact',\n        columns: 2,\n        sections: [\n            {\n                title: 'Basic Information',\n                fields: [\n                    'title',\n                    'slug',\n                    'authorId',\n                    'publishedAt'\n                ]\n            },\n            {\n                title: 'Featured Image',\n                fields: [\n                    'featuredImageUrl'\n                ]\n            },\n            {\n                title: 'Content',\n                fields: [\n                    'excerpt',\n                    'content'\n                ]\n            },\n            {\n                title: 'Categories & Tags',\n                fields: [\n                    'categories',\n                    'tags'\n                ]\n            },\n            {\n                title: 'Publishing Settings',\n                fields: [\n                    'isPublished'\n                ]\n            }\n        ]\n    }\n};\nfunction BlogPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"admin-page\",\n                \"data-section\": \"blog\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_blog_blogs_management__WEBPACK_IMPORTED_MODULE_1__.BlogsManagement, {\n                    config: blogConfig\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/app/admin-dashboard/blog/page.tsx\",\n                    lineNumber: 423,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/app/admin-dashboard/blog/page.tsx\",\n                lineNumber: 422,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/app/admin-dashboard/blog/page.tsx\",\n            lineNumber: 421,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/app/admin-dashboard/blog/page.tsx\",\n        lineNumber: 420,\n        columnNumber: 5\n    }, this);\n}\n_c = BlogPage;\nvar _c;\n$RefreshReg$(_c, \"BlogPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin-dashboard/blog/page.tsx\n"));

/***/ })

});