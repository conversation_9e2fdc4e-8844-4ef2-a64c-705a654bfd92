"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/blog/page",{

/***/ "(app-pages-browser)/./src/components/admin/blog/blogs-management.tsx":
/*!********************************************************!*\
  !*** ./src/components/admin/blog/blogs-management.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlogsManagement: () => (/* binding */ BlogsManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentTextIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentTextIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentTextIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentTextIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentTextIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentTextIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentTextIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentTextIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentTextIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentTextIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentTextIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentTextIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentTextIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentTextIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PowerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentTextIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _blog_modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./blog-modal */ \"(app-pages-browser)/./src/components/admin/blog/blog-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ BlogsManagement auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nfunction BlogAvatar(param) {\n    let { title, featuredImageUrl, size = 'md', className = '', style = {} } = param;\n    _s();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [imageLoading, setImageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // Size configurations\n    const sizeClasses = {\n        xs: 'w-6 h-6',\n        sm: 'w-8 h-8',\n        md: 'w-12 h-12',\n        lg: 'w-16 h-16',\n        xl: 'w-24 h-24',\n        'full-height': 'w-full h-full'\n    };\n    const iconSizes = {\n        xs: 'w-3 h-3',\n        sm: 'w-4 h-4',\n        md: 'w-6 h-6',\n        lg: 'w-8 h-8',\n        xl: 'w-12 h-12',\n        'full-height': 'w-16 h-16'\n    };\n    const textSizes = {\n        xs: 'text-xs',\n        sm: 'text-sm',\n        md: 'text-base',\n        lg: 'text-lg',\n        xl: 'text-xl',\n        'full-height': 'text-4xl'\n    };\n    // Generate initials from blog title\n    const getInitials = (title)=>{\n        return title.split(' ').map((word)=>word.charAt(0)).join('').toUpperCase().slice(0, 2);\n    };\n    // Generate a consistent color based on the blog title\n    const getBackgroundColor = (title)=>{\n        const colors = [\n            'bg-blue-500',\n            'bg-green-500',\n            'bg-purple-500',\n            'bg-pink-500',\n            'bg-indigo-500',\n            'bg-yellow-500',\n            'bg-red-500',\n            'bg-teal-500',\n            'bg-orange-500',\n            'bg-cyan-500'\n        ];\n        let hash = 0;\n        for(let i = 0; i < title.length; i++){\n            hash = title.charCodeAt(i) + ((hash << 5) - hash);\n        }\n        return colors[Math.abs(hash) % colors.length];\n    };\n    const handleImageLoad = ()=>{\n        setImageLoading(false);\n        setImageError(false);\n    };\n    const handleImageError = ()=>{\n        setImageLoading(false);\n        setImageError(true);\n    };\n    const baseClasses = \"\\n    \".concat(sizeClasses[size], \" \\n    rounded-lg \\n    flex \\n    items-center \\n    justify-center \\n    overflow-hidden \\n    \").concat(size === 'full-height' ? 'min-h-[320px]' : '', \"\\n    \").concat(className, \"\\n  \");\n    // If we have a valid featured image URL and no error, show the image\n    if (featuredImageUrl && !imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(baseClasses, \" bg-gray-100 relative\"),\n            style: style,\n            children: [\n                imageLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 w-6 h-6\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: featuredImageUrl,\n                    alt: \"\".concat(title, \" featured image\"),\n                    className: \"\\n            \".concat(size === 'full-height' ? 'w-full h-full object-cover' : 'w-full h-full object-cover', \"\\n            \").concat(imageLoading ? 'opacity-0' : 'opacity-100', \"\\n            transition-opacity duration-200\\n          \"),\n                    onLoad: handleImageLoad,\n                    onError: handleImageError\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 277,\n            columnNumber: 7\n        }, this);\n    }\n    // Fallback: Show initials with colored background\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n        \".concat(baseClasses, \" \\n        \").concat(getBackgroundColor(title), \" \\n        text-white \\n        font-semibold \\n        \").concat(textSizes[size], \"\\n        shadow-sm\\n      \"),\n        style: style,\n        title: title,\n        children: size === 'full-height' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-24 h-24 text-white opacity-80\"\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: getInitials(title)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm opacity-90 break-words line-clamp-3\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 316,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            children: getInitials(title)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 324,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n        lineNumber: 303,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogAvatar, \"yA6MC4/13YXgE42AlKw5vrWMK58=\");\n_c = BlogAvatar;\nfunction BlogsManagement(param) {\n    let { config } = param;\n    var _config_defaultSort, _config_defaultSort1, _config_defaultViewSettings, _config_defaultViewSettings1, _config_defaultViewSettings2, _config_defaultViewSettings3, _config_defaultViewSettings4, _config_defaultViewSettings5, _config_filters, _config_filters1, _config_permissions;\n    _s1();\n    // State management\n    const [blogPosts, setBlogPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [debouncedSearchQuery, setDebouncedSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultSort = config.defaultSort) === null || _config_defaultSort === void 0 ? void 0 : _config_defaultSort.field) || 'updatedAt');\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultSort1 = config.defaultSort) === null || _config_defaultSort1 === void 0 ? void 0 : _config_defaultSort1.direction) === 'asc' ? 'asc' : 'desc');\n    const [selectedPosts, setSelectedPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultViewSettings = config.defaultViewSettings) === null || _config_defaultViewSettings === void 0 ? void 0 : _config_defaultViewSettings.mode) === 'list' || ((_config_defaultViewSettings1 = config.defaultViewSettings) === null || _config_defaultViewSettings1 === void 0 ? void 0 : _config_defaultViewSettings1.mode) === 'grid' ? config.defaultViewSettings.mode : 'list');\n    const [density, setDensity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultViewSettings2 = config.defaultViewSettings) === null || _config_defaultViewSettings2 === void 0 ? void 0 : _config_defaultViewSettings2.density) === 'compact' || ((_config_defaultViewSettings3 = config.defaultViewSettings) === null || _config_defaultViewSettings3 === void 0 ? void 0 : _config_defaultViewSettings3.density) === 'comfortable' || ((_config_defaultViewSettings4 = config.defaultViewSettings) === null || _config_defaultViewSettings4 === void 0 ? void 0 : _config_defaultViewSettings4.density) === 'spacious' ? config.defaultViewSettings.density : 'comfortable');\n    // Create a wrapper function for setDensity\n    const handleDensityChange = (newDensity)=>{\n        setDensity(newDensity);\n    };\n    const [visibleColumns, setVisibleColumns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(Array.isArray((_config_defaultViewSettings5 = config.defaultViewSettings) === null || _config_defaultViewSettings5 === void 0 ? void 0 : _config_defaultViewSettings5.visibleColumns) ? config.defaultViewSettings.visibleColumns : []);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showColumnSelector, setShowColumnSelector] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showWindowList, setShowWindowList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [gridColumns, setGridColumns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(3);\n    // Modal states\n    const [isCreateModalOpen, setIsCreateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isEditModalOpen, setIsEditModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [editingPost, setEditingPost] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Debounce search query\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"BlogsManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchQuery(searchQuery);\n                    setCurrentPage(1); // Reset to first page when searching\n                }\n            }[\"BlogsManagement.useEffect.timer\"], 300);\n            return ({\n                \"BlogsManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"BlogsManagement.useEffect\"];\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        searchQuery\n    ]);\n    // Fetch blog posts\n    const fetchBlogPosts = async function() {\n        let preserveFocus = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        setLoading(true);\n        try {\n            const params = new URLSearchParams({\n                page: currentPage.toString(),\n                limit: (config.pageSize || 10).toString(),\n                search: debouncedSearchQuery,\n                sortBy,\n                sortOrder\n            });\n            // Add filters to params\n            Object.entries(filters).forEach((param)=>{\n                let [key, value] = param;\n                if (value) {\n                    params.append(key, value);\n                }\n            });\n            console.log('Fetching blog posts with params:', params.toString()); // Debug log\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"?\").concat(params));\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('API Error Response:', errorText);\n                throw new Error(\"HTTP error! status: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const data = await response.json();\n            console.log('Received blog posts data:', data); // Debug log\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to fetch blog posts');\n            }\n            setBlogPosts(data.posts || []);\n            setTotalPages(Math.ceil((data.totalCount || 0) / (config.pageSize || 10)));\n            setError(null); // Clear any previous errors on successful fetch\n        } catch (err) {\n            console.error('Error fetching blog posts:', err); // Debug log\n            setError(err instanceof Error ? err.message : 'Failed to fetch blog posts');\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            // Preserve focus when searching\n            const isSearching = debouncedSearchQuery !== '';\n            fetchBlogPosts(isSearching);\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        currentPage,\n        debouncedSearchQuery,\n        sortBy,\n        sortOrder,\n        filters\n    ]);\n    // Handle create\n    const handleCreate = async (formData)=>{\n        try {\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to create blog post (\".concat(response.status, \")\"));\n            }\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to create blog post');\n            }\n            setIsCreateModalOpen(false);\n            fetchBlogPosts();\n            alert('Blog post created successfully!');\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to create blog post';\n            setError(errorMessage);\n            alert(errorMessage);\n            throw err;\n        }\n    };\n    // Handle update\n    const handleUpdate = async (id, formData)=>{\n        try {\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to update blog post (\".concat(response.status, \")\"));\n            }\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to update blog post');\n            }\n            setIsEditModalOpen(false);\n            setEditingPost(null);\n            fetchBlogPosts();\n            alert('Blog post updated successfully!');\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to update blog post';\n            setError(errorMessage);\n            alert(errorMessage);\n            throw err;\n        }\n    };\n    // Handle delete\n    const handleDelete = async (id)=>{\n        try {\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete blog post');\n            fetchBlogPosts();\n            alert('Blog post deleted successfully!');\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to delete blog post';\n            setError(errorMessage);\n            alert(errorMessage);\n            throw err;\n        }\n    };\n    // Handle bulk actions\n    const handleBulkAction = async (action)=>{\n        if (selectedPosts.length === 0) return;\n        setActionLoading(action);\n        try {\n            const promises = selectedPosts.map(async (id)=>{\n                switch(action){\n                    case 'publish':\n                        return fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                            method: 'PUT',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                isPublished: true\n                            })\n                        });\n                    case 'unpublish':\n                        return fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                            method: 'PUT',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                isPublished: false\n                            })\n                        });\n                    case 'delete':\n                        return fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                            method: 'DELETE'\n                        });\n                    default:\n                        throw new Error(\"Unknown bulk action: \".concat(action));\n                }\n            });\n            await Promise.all(promises);\n            setSelectedPosts([]);\n            fetchBlogPosts();\n            alert(\"Bulk \".concat(action, \" completed successfully!\"));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"Failed to \".concat(action, \" blog posts\");\n            setError(errorMessage);\n            alert(errorMessage);\n        } finally{\n            setActionLoading(null);\n        }\n    };\n    // Handle individual actions\n    const handleAction = async (action, item)=>{\n        setActionLoading(\"\".concat(action, \"-\").concat(item.id));\n        try {\n            switch(action){\n                case 'edit':\n                    setEditingPost(item);\n                    setIsEditModalOpen(true);\n                    break;\n                case 'delete':\n                    const confirmMessage = 'Are you sure you want to delete this blog post? This action cannot be undone.';\n                    if (window.confirm(confirmMessage)) {\n                        await handleDelete(item.id);\n                    }\n                    break;\n                case 'toggle-published':\n                    await handleUpdate(item.id, {\n                        isPublished: !item.isPublished\n                    });\n                    break;\n                default:\n                    console.warn(\"Unknown action: \".concat(action));\n            }\n        } finally{\n            setActionLoading(null);\n        }\n    };\n    // Handle sorting\n    const handleSort = (field)=>{\n        if (sortBy === field) {\n            setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n        } else {\n            setSortBy(field);\n            setSortOrder('asc');\n        }\n        setCurrentPage(1);\n    };\n    // Handle selection\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedPosts(blogPosts.map((post)=>post.id));\n        } else {\n            setSelectedPosts([]);\n        }\n    };\n    const handleSelectPost = (id, checked)=>{\n        if (checked) {\n            setSelectedPosts([\n                ...selectedPosts,\n                id\n            ]);\n        } else {\n            setSelectedPosts(selectedPosts.filter((postId)=>postId !== id));\n        }\n    };\n    // Get visible fields for table\n    const getVisibleFields = ()=>{\n        if (visibleColumns.length > 0) {\n            var _config_fields;\n            return ((_config_fields = config.fields) === null || _config_fields === void 0 ? void 0 : _config_fields.filter((field)=>visibleColumns.includes(field.key))) || [];\n        }\n        return config.fields || [];\n    };\n    // Format date\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString();\n    };\n    // Truncate text\n    const truncateText = (text, maxLength)=>{\n        if (text.length <= maxLength) return text;\n        return text.substring(0, maxLength) + '...';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative rounded-lg shadow-sm border border-gray-200/50 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/20\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 644,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-14 w-14 text-lime-600 -mt-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold text-gray-900 mt-2\",\n                                                    children: \"Blog Management\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Create, edit, and manage your blog content.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsCreateModalOpen(true),\n                                        className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Add Blog Post\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 647,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 643,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 rounded-lg shadow-sm border border-gray-200/50 overflow-hidden\",\n                role: \"main\",\n                \"aria-label\": \"Blog management section\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    style: {\n                        paddingBottom: '0'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col space-y-3 lg:hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"Search blog posts...\",\n                                                        value: searchQuery,\n                                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                                        className: \"pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center bg-gray-100 rounded-lg p-0.5 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setViewMode('list'),\n                                                            className: \"flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 \".concat(viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                            title: \"List view\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium hidden xs:inline\",\n                                                                    children: \"List\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 713,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setViewMode('grid'),\n                                                            className: \"flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 \".concat(viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                            title: \"Grid view\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 724,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium hidden xs:inline\",\n                                                                    children: \"Grid\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 725,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 715,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 702,\n                                                    columnNumber: 15\n                                                }, this),\n                                                viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center bg-gray-100 rounded-lg p-0.5 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-gray-700 px-1\",\n                                                            children: \"Col:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 732,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-0.5 flex-1\",\n                                                            children: [\n                                                                1,\n                                                                2,\n                                                                3,\n                                                                4\n                                                            ].map((num)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setGridColumns(num),\n                                                                    className: \"flex-1 px-1.5 py-1 rounded text-xs font-medium \".concat(gridColumns === num ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    title: \"\".concat(num, \" column\").concat(num > 1 ? 's' : ''),\n                                                                    children: num\n                                                                }, num, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 733,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 731,\n                                                    columnNumber: 17\n                                                }, this),\n                                                viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowColumnSelector(!showColumnSelector),\n                                                        className: \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                                        title: \"Columns\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden xs:inline\",\n                                                                children: \"Col\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 761,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-3 w-3 ml-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 762,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 755,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowFilters(!showFilters),\n                                                        className: \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium rounded-lg border \".concat(showFilters || Object.keys(filters).some((key)=>filters[key]) ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                                                        title: \"Filters\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 778,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden xs:inline\",\n                                                                children: \"Filter\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            Object.keys(filters).some((key)=>filters[key]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-0.5 inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                children: Object.values(filters).filter(Boolean).length\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 768,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowWindowList(!showWindowList),\n                                                        className: \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                                        title: \"Density\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 795,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden xs:inline\",\n                                                                children: density.charAt(0).toUpperCase() + density.slice(1)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 796,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-3 w-3 ml-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 797,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 790,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 789,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 700,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:flex items-center justify-between gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 flex-1 max-w-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 809,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Search blog posts by title, content, excerpt...\",\n                                                            value: searchQuery,\n                                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                                            className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 808,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowFilters(!showFilters),\n                                                            className: \"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(showFilters || Object.keys(filters).some((key)=>filters[key]) ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                                                            title: \"Show/hide filters\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 830,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Filters\",\n                                                                Object.keys(filters).some((key)=>filters[key]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                    children: Object.values(filters).filter(Boolean).length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 833,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 837,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 821,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-full right-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                                children: \"Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 845,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>setShowFilters(false),\n                                                                                className: \"text-gray-400 hover:text-gray-600\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 850,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 846,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 844,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: (_config_filters = config.filters) === null || _config_filters === void 0 ? void 0 : _config_filters.map((filter)=>{\n                                                                            var _filter_options;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"block text-xs font-medium text-gray-700 mb-2\",\n                                                                                        children: filter.label\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 857,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                        value: filters[filter.key] || '',\n                                                                                        onChange: (e)=>{\n                                                                                            const newFilters = {\n                                                                                                ...filters\n                                                                                            };\n                                                                                            if (e.target.value) {\n                                                                                                newFilters[filter.key] = e.target.value;\n                                                                                            } else {\n                                                                                                delete newFilters[filter.key];\n                                                                                            }\n                                                                                            setFilters(newFilters);\n                                                                                        },\n                                                                                        className: \"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                                                        children: (_filter_options = filter.options) === null || _filter_options === void 0 ? void 0 : _filter_options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: option.value,\n                                                                                                children: option.label\n                                                                                            }, option.value, false, {\n                                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                                lineNumber: 874,\n                                                                                                columnNumber: 37\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 860,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, filter.key, true, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 856,\n                                                                                columnNumber: 31\n                                                                            }, this);\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 854,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-end mt-4 pt-4 border-t border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                setFilters({});\n                                                                                setShowFilters(false);\n                                                                            },\n                                                                            className: \"px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg\",\n                                                                            children: \"Clear All\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 884,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 883,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 843,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 842,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 820,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 807,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"View:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 904,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center bg-gray-100 rounded-lg p-1 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setViewMode('list'),\n                                                                    className: \"px-3 py-2 rounded-md flex items-center gap-2 \".concat(viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    title: \"List view\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"h-5 w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 915,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: \"List\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 916,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 906,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setViewMode('grid'),\n                                                                    className: \"px-3 py-2 rounded-md flex items-center gap-2 \".concat(viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    title: \"Grid view\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"h-5 w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 927,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: \"Grid\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 928,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 918,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 905,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 903,\n                                                    columnNumber: 19\n                                                }, this),\n                                                viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"Columns:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 936,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center bg-gray-100 rounded-lg p-1 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setGridColumns(1),\n                                                                    className: \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 1 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    title: \"1 column\",\n                                                                    children: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 938,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setGridColumns(2),\n                                                                    className: \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 2 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    title: \"2 columns\",\n                                                                    children: \"2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 949,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setGridColumns(3),\n                                                                    className: \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 3 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    title: \"3 columns\",\n                                                                    children: \"3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 960,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setGridColumns(4),\n                                                                    className: \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 4 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    title: \"4 columns\",\n                                                                    children: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 971,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 937,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 935,\n                                                    columnNumber: 21\n                                                }, this),\n                                                viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowColumnSelector(!showColumnSelector),\n                                                            className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            title: \"Select columns to display\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 994,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Columns\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 996,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 989,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        showColumnSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs font-medium text-gray-500 uppercase tracking-wide mb-2\",\n                                                                        children: \"Show Columns\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1003,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    config.columns.map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"flex items-center space-x-2 py-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: visibleColumns.includes(column.key),\n                                                                                    onChange: (e)=>{\n                                                                                        if (e.target.checked) {\n                                                                                            setVisibleColumns((prev)=>[\n                                                                                                    ...prev,\n                                                                                                    column.key\n                                                                                                ]);\n                                                                                        } else {\n                                                                                            setVisibleColumns((prev)=>prev.filter((col)=>col !== column.key));\n                                                                                        }\n                                                                                    },\n                                                                                    className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1006,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-700\",\n                                                                                    children: column.label\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1018,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, column.key, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1005,\n                                                                            columnNumber: 31\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1002,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1001,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 988,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowWindowList(!showWindowList),\n                                                            className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            title: \"Select density\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1036,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                density.charAt(0).toUpperCase() + density.slice(1),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1038,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1031,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showWindowList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-full left-0 mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-1\",\n                                                                children: [\n                                                                    'compact',\n                                                                    'comfortable',\n                                                                    'spacious'\n                                                                ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setDensity(option);\n                                                                            setShowWindowList(false);\n                                                                        },\n                                                                        className: \"w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 \".concat(density === option ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'),\n                                                                        children: option.charAt(0).toUpperCase() + option.slice(1)\n                                                                    }, option, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1046,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1044,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1043,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1030,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 901,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 805,\n                                    columnNumber: 15\n                                }, this),\n                                showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: \"Filters\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1072,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowFilters(false),\n                                                    className: \"text-gray-400 hover:text-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1077,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1073,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1071,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: (_config_filters1 = config.filters) === null || _config_filters1 === void 0 ? void 0 : _config_filters1.map((filter)=>{\n                                                var _filter_options;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-xs font-medium text-gray-700 mb-2\",\n                                                            children: filter.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1084,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: filters[filter.key] || '',\n                                                            onChange: (e)=>{\n                                                                const newFilters = {\n                                                                    ...filters\n                                                                };\n                                                                if (e.target.value) {\n                                                                    newFilters[filter.key] = e.target.value;\n                                                                } else {\n                                                                    delete newFilters[filter.key];\n                                                                }\n                                                                setFilters(newFilters);\n                                                            },\n                                                            className: \"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                            children: (_filter_options = filter.options) === null || _filter_options === void 0 ? void 0 : _filter_options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: option.value,\n                                                                    children: option.label\n                                                                }, option.value, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1101,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1087,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, filter.key, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1083,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1081,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-end mt-4 pt-4 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setFilters({});\n                                                    setShowFilters(false);\n                                                },\n                                                className: \"px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg\",\n                                                children: \"Clear All\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1111,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1110,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1070,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 682,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                config.enableBulkActions && selectedPosts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-bold text-blue-800\",\n                                                children: [\n                                                    selectedPosts.length,\n                                                    \" blog post(s) selected\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1130,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleBulkAction('publish'),\n                                                        className: \"px-4 py-2 text-sm font-bold bg-white text-green-700 rounded-lg hover:bg-green-50 transition-colors shadow-md border-2 border-green-500\",\n                                                        children: \"Publish\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1134,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleBulkAction('unpublish'),\n                                                        className: \"px-4 py-2 text-sm font-bold bg-white text-orange-700 rounded-lg hover:bg-orange-50 transition-colors shadow-md border-2 border-orange-500\",\n                                                        children: \"Unpublish\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1140,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleBulkAction('delete'),\n                                                        className: \"px-4 py-2 text-sm font-bold bg-white text-red-700 rounded-lg hover:bg-red-50 transition-colors shadow-md border-2 border-red-500\",\n                                                        children: \"Delete\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1146,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1133,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1129,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1128,\n                                    columnNumber: 15\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1163,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1162,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-medium text-red-800\",\n                                                            children: \"Error\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1166,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 text-sm text-red-700\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: error\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1168,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1167,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    setError(null);\n                                                                    fetchBlogPosts();\n                                                                },\n                                                                className: \"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200\",\n                                                                children: \"Try Again\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1171,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1170,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1165,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1161,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1160,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1159,\n                                    columnNumber: 15\n                                }, this),\n                                loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1191,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-3 text-gray-600\",\n                                                children: \"Loading blog posts...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1192,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1190,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1189,\n                                    columnNumber: 15\n                                }, this),\n                                !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4\",\n                                    children: blogPosts.length === 0 ? /* Empty State */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-12 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"mx-auto h-12 w-12 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1203,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"mt-2 text-sm font-medium text-gray-900\",\n                                                children: \"No blog posts found\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1204,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-gray-500\",\n                                                children: debouncedSearchQuery ? 'Try adjusting your search terms.' : 'Get started by creating your first blog post.'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1205,\n                                                columnNumber: 21\n                                            }, this),\n                                            ((_config_permissions = config.permissions) === null || _config_permissions === void 0 ? void 0 : _config_permissions.create) && !debouncedSearchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsCreateModalOpen(true),\n                                                    className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                                    children: \"Add Blog Post\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1210,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1209,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1202,\n                                        columnNumber: 19\n                                    }, this) : viewMode === 'list' ? /* Table View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-e472e241e333a08e\" + \" \" + \"overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                id: \"e472e241e333a08e\",\n                                                children: \".density-compact.jsx-e472e241e333a08e th.jsx-e472e241e333a08e,.density-compact.jsx-e472e241e333a08e td.jsx-e472e241e333a08e{padding-top:4px!important;padding-bottom:4px!important}.density-comfortable.jsx-e472e241e333a08e th.jsx-e472e241e333a08e,.density-comfortable.jsx-e472e241e333a08e td.jsx-e472e241e333a08e{padding-top:16px!important;padding-bottom:16px!important}.density-spacious.jsx-e472e241e333a08e th.jsx-e472e241e333a08e,.density-spacious.jsx-e472e241e333a08e td.jsx-e472e241e333a08e{padding-top:32px!important;padding-bottom:32px!important}\"\n                                            }, void 0, false, void 0, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-e472e241e333a08e\" + \" \" + \"overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"jsx-e472e241e333a08e\" + \" \" + \"min-w-full divide-y divide-gray-200 density-\".concat(density),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            className: \"jsx-e472e241e333a08e\" + \" \" + \"bg-gray-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"jsx-e472e241e333a08e\",\n                                                                children: [\n                                                                    config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"jsx-e472e241e333a08e\" + \" \" + \"w-4 px-6 py-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: selectedPosts.length === blogPosts.length && blogPosts.length > 0,\n                                                                            onChange: (e)=>handleSelectAll(e.target.checked),\n                                                                            style: {\n                                                                                backgroundColor: 'white'\n                                                                            },\n                                                                            className: \"jsx-e472e241e333a08e\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1243,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1242,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    getVisibleFields().map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"jsx-e472e241e333a08e\" + \" \" + \"px-6 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleSort(field.key),\n                                                                                className: \"jsx-e472e241e333a08e\" + \" \" + \"flex items-center space-x-1 hover:text-gray-700\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-e472e241e333a08e\",\n                                                                                        children: field.label\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1263,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    sortBy === field.key && (sortOrder === 'asc' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1265,\n                                                                                        columnNumber: 59\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1265,\n                                                                                        columnNumber: 97\n                                                                                    }, this))\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1259,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, field.key, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1255,\n                                                                            columnNumber: 31\n                                                                        }, this)),\n                                                                    config.actions && config.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"jsx-e472e241e333a08e\" + \" \" + \"px-6 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Actions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1273,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1239,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1238,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            className: \"jsx-e472e241e333a08e\" + \" \" + \"bg-white divide-y divide-gray-200\",\n                                                            children: blogPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    className: \"jsx-e472e241e333a08e\" + \" \" + \"hover:bg-gray-50 \".concat(selectedPosts.includes(post.id) ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''),\n                                                                    children: [\n                                                                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"jsx-e472e241e333a08e\" + \" \" + \"px-6\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: selectedPosts.includes(post.id),\n                                                                                onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                                                                style: {\n                                                                                    backgroundColor: 'white'\n                                                                                },\n                                                                                className: \"jsx-e472e241e333a08e\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1287,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1286,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        getVisibleFields().map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"jsx-e472e241e333a08e\" + \" \" + \"px-6 whitespace-nowrap\",\n                                                                                children: field.key === 'title' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-e472e241e333a08e\" + \" \" + \"flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BlogAvatar, {\n                                                                                            title: post.title,\n                                                                                            featuredImageUrl: post.featuredImageUrl,\n                                                                                            size: \"sm\",\n                                                                                            className: \"mr-3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                            lineNumber: 1302,\n                                                                                            columnNumber: 39\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"jsx-e472e241e333a08e\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"jsx-e472e241e333a08e\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                                                                    children: post.title\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                                    lineNumber: 1309,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"jsx-e472e241e333a08e\" + \" \" + \"text-sm text-gray-500\",\n                                                                                                    children: post.slug\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                                    lineNumber: 1310,\n                                                                                                    columnNumber: 41\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                            lineNumber: 1308,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1301,\n                                                                                    columnNumber: 37\n                                                                                }, this) : field.key === 'isPublished' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-e472e241e333a08e\" + \" \" + \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                                    children: post.isPublished ? 'Published' : 'Draft'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1314,\n                                                                                    columnNumber: 37\n                                                                                }, this) : field.key === 'excerpt' || field.key === 'content' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    title: post[field.key],\n                                                                                    className: \"jsx-e472e241e333a08e\" + \" \" + \"text-sm text-gray-900 max-w-xs truncate\",\n                                                                                    children: truncateText(post[field.key] || '', 50)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1322,\n                                                                                    columnNumber: 37\n                                                                                }, this) : field.key === 'tags' || field.key === 'categories' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-e472e241e333a08e\" + \" \" + \"text-sm text-gray-900\",\n                                                                                    children: post[field.key] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-e472e241e333a08e\" + \" \" + \"flex flex-wrap gap-1\",\n                                                                                        children: [\n                                                                                            post[field.key].split(',').slice(0, 2).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"jsx-e472e241e333a08e\" + \" \" + \"inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\",\n                                                                                                    children: tag.trim()\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                                    lineNumber: 1330,\n                                                                                                    columnNumber: 45\n                                                                                                }, this)),\n                                                                                            post[field.key].split(',').length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"jsx-e472e241e333a08e\" + \" \" + \"text-xs text-gray-500\",\n                                                                                                children: [\n                                                                                                    \"+\",\n                                                                                                    post[field.key].split(',').length - 2,\n                                                                                                    \" more\"\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                                lineNumber: 1335,\n                                                                                                columnNumber: 45\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1328,\n                                                                                        columnNumber: 41\n                                                                                    }, this) : '-'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1326,\n                                                                                    columnNumber: 37\n                                                                                }, this) : field.key === 'createdAt' || field.key === 'updatedAt' || field.key === 'publishedAt' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-e472e241e333a08e\" + \" \" + \"text-sm text-gray-500\",\n                                                                                    children: post[field.key] ? formatDate(post[field.key]) : '-'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1341,\n                                                                                    columnNumber: 37\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-e472e241e333a08e\" + \" \" + \"text-sm text-gray-900\",\n                                                                                    children: post[field.key] || '-'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1345,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, field.key, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1299,\n                                                                                columnNumber: 33\n                                                                            }, this)),\n                                                                        config.actions && config.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"jsx-e472e241e333a08e\" + \" \" + \"px-6 whitespace-nowrap text-right text-sm font-medium\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-e472e241e333a08e\" + \" \" + \"flex items-center space-x-2\",\n                                                                                children: config.actions.map((action)=>{\n                                                                                    const isLoading = actionLoading === \"\".concat(action.action, \"-\").concat(post.id);\n                                                                                    const IconComponent = action.icon === 'EyeIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"] : action.icon === 'PencilIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"] : action.icon === 'PowerIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"] : action.icon === 'TrashIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"] : _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleAction(action.action, post),\n                                                                                        disabled: isLoading,\n                                                                                        title: action.tooltip,\n                                                                                        className: \"jsx-e472e241e333a08e\" + \" \" + \"p-1 rounded-md transition-colors \".concat(action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' : action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' : action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' : action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' : 'text-gray-600 hover:bg-gray-50', \" \").concat(isLoading ? 'opacity-50 cursor-not-allowed' : ''),\n                                                                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"jsx-e472e241e333a08e\" + \" \" + \"animate-spin rounded-full h-4 w-4 border-b-2 border-current\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                            lineNumber: 1378,\n                                                                                            columnNumber: 45\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                            className: \"jsx-e472e241e333a08e\" + \" \" + \"w-4 h-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                            lineNumber: 1380,\n                                                                                            columnNumber: 45\n                                                                                        }, this)\n                                                                                    }, action.action, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1364,\n                                                                                        columnNumber: 41\n                                                                                    }, this);\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1355,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1354,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, post.id, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1281,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1279,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1237,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1236,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1221,\n                                        columnNumber: 19\n                                    }, this) : viewMode === 'grid' ? /* Grid View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                                            children: blogPosts.map((post)=>{\n                                                var _config_actions;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow \".concat(selectedPosts.includes(post.id) ? 'ring-2 ring-blue-500 bg-blue-50' : ''),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"aspect-video bg-gray-100 rounded-t-lg overflow-hidden\",\n                                                            children: post.featuredImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: post.featuredImageUrl,\n                                                                alt: post.title,\n                                                                className: \"w-full h-full object-cover\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1405,\n                                                                columnNumber: 31\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full h-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    className: \"w-12 h-12 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1412,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1411,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1403,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 mb-2 line-clamp-2\",\n                                                                    children: post.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1420,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                post.excerpt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 mb-3 line-clamp-3\",\n                                                                    children: post.excerpt\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1426,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                                    children: post.isPublished ? 'Published' : 'Draft'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1435,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-gray-500\",\n                                                                                    children: formatDate(post.updatedAt)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1442,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1434,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        post.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap gap-1\",\n                                                                            children: post.categories.split(',').slice(0, 2).map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\",\n                                                                                    children: category.trim()\n                                                                                }, index, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1451,\n                                                                                    columnNumber: 37\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1449,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1432,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between pt-3 border-t border-gray-100\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: (_config_actions = config.actions) === null || _config_actions === void 0 ? void 0 : _config_actions.map((action)=>{\n                                                                                const isLoading = actionLoading === \"\".concat(action.action, \"-\").concat(post.id);\n                                                                                const IconComponent = action.icon === 'EyeIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"] : action.icon === 'PencilIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"] : action.icon === 'PowerIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"] : action.icon === 'TrashIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"] : _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleAction(action.action, post),\n                                                                                    disabled: isLoading,\n                                                                                    className: \"p-1.5 rounded-md transition-colors \".concat(action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' : action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' : action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' : action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' : 'text-gray-600 hover:bg-gray-50', \" \").concat(isLoading ? 'opacity-50 cursor-not-allowed' : ''),\n                                                                                    title: action.tooltip,\n                                                                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-current\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1484,\n                                                                                        columnNumber: 41\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1486,\n                                                                                        columnNumber: 41\n                                                                                    }, this)\n                                                                                }, action.action, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1470,\n                                                                                    columnNumber: 37\n                                                                                }, this);\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1461,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: selectedPosts.includes(post.id),\n                                                                            onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                                                            className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white\",\n                                                                            style: {\n                                                                                backgroundColor: 'white'\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1495,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1460,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1418,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, post.id, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1399,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1397,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1396,\n                                        columnNumber: 19\n                                    }, this) : /* Card View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: blogPosts.map((post)=>{\n                                                var _config_actions;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow \".concat(selectedPosts.includes(post.id) ? 'ring-2 ring-blue-500 bg-blue-50' : ''),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-48 h-32 bg-gray-100 rounded-l-lg overflow-hidden flex-shrink-0\",\n                                                                children: post.featuredImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: post.featuredImageUrl,\n                                                                    alt: post.title,\n                                                                    className: \"w-full h-full object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1521,\n                                                                    columnNumber: 33\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        className: \"w-8 h-8 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1528,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1527,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1519,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                                                    children: post.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1538,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-500 mb-2\",\n                                                                                    children: post.slug\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1541,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                post.excerpt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                                                                    children: post.excerpt\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1547,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                                            children: post.isPublished ? 'Published' : 'Draft'\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                            lineNumber: 1554,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: [\n                                                                                                \"Updated: \",\n                                                                                                formatDate(post.updatedAt)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                            lineNumber: 1561,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        post.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: [\n                                                                                                \"Category: \",\n                                                                                                post.categories.split(',')[0].trim()\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                            lineNumber: 1563,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1553,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1536,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 ml-4\",\n                                                                            children: [\n                                                                                (_config_actions = config.actions) === null || _config_actions === void 0 ? void 0 : _config_actions.map((action)=>{\n                                                                                    const isLoading = actionLoading === \"\".concat(action.action, \"-\").concat(post.id);\n                                                                                    const IconComponent = action.icon === 'EyeIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"] : action.icon === 'PencilIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"] : action.icon === 'PowerIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"] : action.icon === 'TrashIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"] : _barrel_optimize_names_AdjustmentsHorizontalIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentTextIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleAction(action.action, post),\n                                                                                        disabled: isLoading,\n                                                                                        className: \"p-2 rounded-md transition-colors \".concat(action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' : action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' : action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' : action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' : 'text-gray-600 hover:bg-gray-50', \" \").concat(isLoading ? 'opacity-50 cursor-not-allowed' : ''),\n                                                                                        title: action.tooltip,\n                                                                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-current\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                            lineNumber: 1592,\n                                                                                            columnNumber: 43\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                            className: \"w-4 h-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                            lineNumber: 1594,\n                                                                                            columnNumber: 43\n                                                                                        }, this)\n                                                                                    }, action.action, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1578,\n                                                                                        columnNumber: 39\n                                                                                    }, this);\n                                                                                }),\n                                                                                config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: selectedPosts.includes(post.id),\n                                                                                    onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                                                                    className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white\",\n                                                                                    style: {\n                                                                                        backgroundColor: 'white'\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1602,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1569,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1535,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1534,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1517,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, post.id, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1514,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1512,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1511,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1199,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 680,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 675,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_modal__WEBPACK_IMPORTED_MODULE_3__.BlogModal, {\n                isOpen: isCreateModalOpen,\n                onClose: ()=>setIsCreateModalOpen(false),\n                onSubmit: handleCreate,\n                title: \"Create New Blog Post\"\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 1626,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_modal__WEBPACK_IMPORTED_MODULE_3__.BlogModal, {\n                isOpen: isEditModalOpen,\n                onClose: ()=>{\n                    setIsEditModalOpen(false);\n                    setEditingPost(null);\n                },\n                onSubmit: async (formData)=>{\n                    if (editingPost) {\n                        await handleUpdate(editingPost.id, formData);\n                    }\n                },\n                title: \"Edit Blog Post\",\n                initialData: editingPost !== null && editingPost !== void 0 ? editingPost : undefined\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 1633,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n        lineNumber: 641,\n        columnNumber: 5\n    }, this);\n}\n_s1(BlogsManagement, \"d0SZSnTsTA3CnpWBCqgHJVi6oss=\");\n_c1 = BlogsManagement;\nvar _c, _c1;\n$RefreshReg$(_c, \"BlogAvatar\");\n$RefreshReg$(_c1, \"BlogsManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/blog/blogs-management.tsx\n"));

/***/ })

});