"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/blog/page",{

/***/ "(app-pages-browser)/./src/app/admin-dashboard/blog/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/admin-dashboard/blog/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nconst blogConfig = {\n    title: 'Blog Posts',\n    description: 'Create, edit, and manage your blog content',\n    endpoint: 'blog',\n    // Table columns configuration\n    columns: [\n        {\n            key: 'title',\n            label: 'Title',\n            sortable: true,\n            searchable: true,\n            width: '30%'\n        },\n        {\n            key: 'excerpt',\n            label: 'Excerpt',\n            sortable: false,\n            searchable: true,\n            width: '25%'\n        },\n        {\n            key: 'isPublished',\n            label: 'Status',\n            sortable: true,\n            searchable: false,\n            width: '10%'\n        },\n        {\n            key: 'categories',\n            label: 'Category',\n            sortable: true,\n            searchable: true,\n            width: '15%'\n        },\n        {\n            key: 'tags',\n            label: 'Tags',\n            sortable: false,\n            searchable: true,\n            width: '15%'\n        },\n        {\n            key: 'updatedAt',\n            label: 'Last Updated',\n            sortable: true,\n            searchable: false,\n            width: '15%'\n        }\n    ],\n    // Filters configuration\n    filters: [\n        {\n            key: 'isPublished',\n            label: 'Publication Status',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Posts'\n                },\n                {\n                    value: 'true',\n                    label: 'Published'\n                },\n                {\n                    value: 'false',\n                    label: 'Draft'\n                }\n            ]\n        },\n        {\n            key: 'categories',\n            label: 'Category',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Categories'\n                },\n                {\n                    value: 'Web Development',\n                    label: 'Web Development'\n                },\n                {\n                    value: 'Mobile Development',\n                    label: 'Mobile Development'\n                },\n                {\n                    value: 'Cloud Computing',\n                    label: 'Cloud Computing'\n                },\n                {\n                    value: 'Programming',\n                    label: 'Programming'\n                },\n                {\n                    value: 'Security',\n                    label: 'Security'\n                },\n                {\n                    value: 'AI/ML',\n                    label: 'AI/ML'\n                }\n            ]\n        }\n    ],\n    // Bulk actions configuration\n    bulkActions: [\n        {\n            action: 'publish',\n            label: 'Publish',\n            icon: 'PowerIcon',\n            variant: 'primary',\n            confirmationMessage: 'Are you sure you want to publish the selected blog posts?'\n        },\n        {\n            action: 'unpublish',\n            label: 'Unpublish',\n            icon: 'PowerIcon',\n            variant: 'secondary',\n            confirmationMessage: 'Are you sure you want to unpublish the selected blog posts?'\n        },\n        {\n            action: 'delete',\n            label: 'Delete',\n            icon: 'TrashIcon',\n            variant: 'danger',\n            confirmationMessage: 'Are you sure you want to delete the selected blog posts? This action cannot be undone.'\n        }\n    ],\n    // Action buttons for each row\n    actions: [\n        {\n            action: 'view',\n            label: 'View',\n            icon: 'EyeIcon',\n            variant: 'secondary',\n            tooltip: 'View blog post details'\n        },\n        {\n            action: 'edit',\n            label: 'Edit',\n            icon: 'PencilIcon',\n            variant: 'primary',\n            tooltip: 'Edit blog post'\n        },\n        {\n            action: 'toggle-published',\n            label: 'Toggle Published',\n            icon: 'PowerIcon',\n            variant: 'warning',\n            tooltip: 'Publish/Unpublish blog post'\n        },\n        {\n            action: 'delete',\n            label: 'Delete',\n            icon: 'TrashIcon',\n            variant: 'danger',\n            tooltip: 'Delete blog post'\n        }\n    ],\n    fields: [\n        {\n            key: 'title',\n            label: 'Title',\n            type: 'text',\n            required: true,\n            searchable: true,\n            placeholder: 'e.g., How to Build Amazing Web Applications'\n        },\n        {\n            key: 'slug',\n            label: 'Slug',\n            type: 'text',\n            required: true,\n            searchable: true,\n            placeholder: 'e.g., how-to-build-amazing-web-applications'\n        },\n        {\n            key: 'excerpt',\n            label: 'Excerpt',\n            type: 'textarea',\n            searchable: true,\n            placeholder: 'Brief description of the blog post...',\n            rows: 3\n        },\n        {\n            key: 'content',\n            label: 'Content',\n            type: 'textarea',\n            required: true,\n            searchable: true,\n            placeholder: 'Write your blog post content here...',\n            rows: 8\n        },\n        {\n            key: 'featuredImageUrl',\n            label: 'Featured Image',\n            type: 'url',\n            searchable: false,\n            placeholder: 'Enter image URL or click Upload to select file'\n        },\n        {\n            key: 'authorId',\n            label: 'Author ID',\n            type: 'text',\n            searchable: false,\n            placeholder: 'e.g., author-123'\n        },\n        {\n            key: 'categories',\n            label: 'Categories',\n            type: 'text',\n            searchable: true,\n            placeholder: 'e.g., Web Development, Technology'\n        },\n        {\n            key: 'tags',\n            label: 'Tags',\n            type: 'text',\n            searchable: true,\n            placeholder: 'e.g., react, nextjs, javascript, tutorial'\n        },\n        {\n            key: 'isPublished',\n            label: 'Published',\n            type: 'boolean',\n            defaultValue: false,\n            searchable: false\n        },\n        {\n            key: 'publishedAt',\n            label: 'Published Date',\n            type: 'datetime-local',\n            searchable: false\n        }\n    ],\n    permissions: {\n        create: true,\n        read: true,\n        update: true,\n        delete: true,\n        export: true\n    },\n    searchPlaceholder: 'Search blog posts by title, content, excerpt, categories, tags...',\n    defaultSort: {\n        field: 'updatedAt',\n        direction: 'desc'\n    },\n    pageSize: 10,\n    enableSearch: true,\n    enableFilters: true,\n    enableBulkActions: true,\n    enableExport: true,\n    enableViewControls: true,\n    enableDensityControls: true,\n    enableColumnVisibility: true,\n    defaultViewSettings: {\n        mode: 'list',\n        density: 'comfortable',\n        visibleColumns: [\n            'title',\n            'excerpt',\n            'isPublished',\n            'categories',\n            'tags',\n            'updatedAt'\n        ]\n    },\n    // Form layout configuration\n    formLayout: {\n        type: 'compact',\n        columns: 2,\n        sections: [\n            {\n                title: 'Basic Information',\n                fields: [\n                    'title',\n                    'slug',\n                    'authorId',\n                    'publishedAt'\n                ]\n            },\n            {\n                title: 'Featured Image',\n                fields: [\n                    'featuredImageUrl'\n                ]\n            },\n            {\n                title: 'Content',\n                fields: [\n                    'excerpt',\n                    'content'\n                ]\n            },\n            {\n                title: 'Categories & Tags',\n                fields: [\n                    'categories',\n                    'tags'\n                ]\n            },\n            {\n                title: 'Publishing Settings',\n                fields: [\n                    'isPublished'\n                ]\n            }\n        ]\n    }\n};\nfunction BlogPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"admin-page\",\n                \"data-section\": \"blog\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BlogManagerNew, {\n                    config: blogConfig\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/app/admin-dashboard/blog/page.tsx\",\n                    lineNumber: 423,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/app/admin-dashboard/blog/page.tsx\",\n                lineNumber: 422,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/app/admin-dashboard/blog/page.tsx\",\n            lineNumber: 421,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/app/admin-dashboard/blog/page.tsx\",\n        lineNumber: 420,\n        columnNumber: 5\n    }, this);\n}\n_c = BlogPage;\nvar _c;\n$RefreshReg$(_c, \"BlogPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin-dashboard/blog/page.tsx\n"));

/***/ })

});