/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/components/modals.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/* ========================================
   REUSABLE MODAL COMPONENT STYLES
   Organized by component sections for better maintainability
   
   NOTE: This file uses Tailwind CSS @apply directives and vendor-specific
   properties like text-fill-color. Linter warnings for these are expected
   and acceptable in a Tailwind CSS project.
   ======================================== */

/* ========================================
   BACKDROP STYLES
   ======================================== */

/* Mobile sidebar overlay/backdrop */
.sidebar-overlay {
  /* stylelint-disable-next-line at-rule-no-unknown */
  position: fixed;
  inset: 0px;
  z-index: 40;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
  --tw-bg-opacity: 0.75;
}

.sidebar-overlay.lg-hidden {
  /* stylelint-disable-next-line at-rule-no-unknown */
}

@media (min-width: 1024px) {
  .sidebar-overlay.lg-hidden {
    display: none;
  }
}

/* General overlay backdrop for modals */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 40;
}

.modal-overlay.dark {
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
  --tw-bg-opacity: 0.5;
}

.modal-overlay.light {
  background-color: transparent;
  --tw-bg-opacity: 0.5;
}

/* Backdrop blur effects */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.backdrop-blur-md {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.backdrop-blur-lg {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

/* ========================================
   CONTAINER STYLES
   ======================================== */

/* Modal container base */
.modal-container {
  position: fixed;
  display: flex;
  cursor: move;
  flex-direction: column;
  overflow: hidden;
  border-radius: 0.75rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 0px;
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-container.sample-style {
  overflow: hidden;
  border-radius: 0.75rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 0px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  isolation: isolate;
  contain: layout style paint;
  resize: none;
  transition: box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  animation: modalAppear 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes modalAppear {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-container.draggable {
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.modal-container.no-backdrop {
  /* No backdrop styling - modal is draggable without overlay */
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
}

/* Modal container with motion/framer-motion support */
.modal-container-motion {
  overflow: hidden;
  border-radius: 0.75rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding: 0px;
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.modal-container-motion.dragging {
  cursor: grabbing;
}

.modal-container-motion.default {
  cursor: default;
}

/* Modal container positioning */
.modal-positioned {
  position: fixed;
}

.modal-positioned.dragging {
  cursor: grabbing;
}

.modal-positioned.default {
  cursor: default;
}

/* Modal z-index utilities - removed to prevent overrides */

/* Draggable modal utility class */
.draggable-modal {
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Drag handle utility class */
.drag-handle {
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Modal dynamic styles (from modal-system.tsx) */
.modal-container-dynamic {
  position: fixed;
}

.modal-container-dragging {
  cursor: grabbing;
}

.modal-container-default {
  cursor: default;
}

/* Modal component specific containers */
.modal-payment-container {
  /* Dynamic width and height will be set via inline styles */
  position: relative;
}

.modal-project-container {
  /* Dynamic width and height will be set via inline styles */
  position: relative;
}

.modal-blog-container {
  /* Dynamic top and left will be set via inline styles */
}

/* ========================================
   HEADER STYLES
   ======================================== */

/* Modal header base styles */
.modal-header {
  background: linear-gradient(to right, #3b82f6, #2563eb);
  border-bottom: 2px solid #1d4ed8;
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: -8px;
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  opacity: 1;
  background-color: transparent;
}

/* Modal header with motion support and icon colors - matching sample modal */
.modal-header-motion {
  background: linear-gradient(to right, #3b82f6, #2563eb);
  border-bottom: 2px solid #1d4ed8;
  padding: 16px 24px;
  height: 80px;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: -8px;
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  opacity: 1;
  background-color: transparent;
}

.modal-header-motion.blue-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}

.modal-header-motion.green-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #16a34a var(--tw-gradient-to-position);
}

.modal-header-motion.purple-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(126 34 206 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}

.modal-header-motion.orange-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(194 65 12 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);
}

.modal-header-motion.red-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);
}

/* Modal header content and icon styles */
.modal-header-content {
  flex: 1;
}

.modal-header-icon {
  padding: 0px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header-icon svg {
  width: 24px;
  height: 24px;
  color: white;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.modal-header-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.modal-header-subtitle {
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: rgba(255, 255, 255, 0.9);
}

.modal-header-close {
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.modal-header-close svg {
  width: 20px;
  height: 20px;
  color: white;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.modal-header-motion-content {
  display: flex;
  align-items: center;
}

.modal-header-motion-content > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.modal-header-motion-icon {
  padding: 0px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.modal-header-motion-icon svg {
  width: 24px;
  height: 24px;
  stroke: white;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}

.modal-header-motion-text {
  flex: 1;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.modal-header-motion-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0;
  line-height: 1.2;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.modal-header-motion-subtitle {
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.3;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: rgba(255, 255, 255, 0.9);
}

.modal-header-motion-close {
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header-motion-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.modal-header-motion-close svg {
  width: 20px;
  height: 20px;
  stroke: white;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}

/* ========================================
   BODY STYLES
   ======================================== */

/* Modal content base */
.modal-content {
  flex: 1 1 0%;
  cursor: default;
}

/* Custom scrollbar styling for modal content */
.modal-content::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: #94a3b8;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Firefox scrollbar styling */
.modal-content {
  scrollbar-width: thin;
  scrollbar-color: #94a3b8 #f1f5f9;
}

.modal-content-motion {
  flex: 1 1 0%;
  cursor: default;
  overflow-y: auto;
}

/* Modal form styles */
.modal-form > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.modal-form {
  padding: 1rem;
}

.modal-form.compact > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.modal-form.compact {
  padding: 0.75rem;
}

/* Modal form sections */
.modal-form-section > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.modal-form-section.compact > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.modal-form-section-motion {
  background-color: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.modal-form-section-motion.compact {
  padding: 0.75rem;
}

.modal-form-section-motion.default {
  padding: 1rem;
}

/* Removed - icons are now directly inside titles */

.modal-form-section-motion-header.compact {
  margin-bottom: 0.5rem;
}

.modal-form-section-motion-header.default {
  margin-bottom: 0.75rem;
}

.modal-form-section-motion-icon {
  display: flex;
  height: 1.25rem;
  width: 1.25rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
}

.modal-form-section-motion-icon.blue {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.modal-form-section-motion-icon.green {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.modal-form-section-motion-icon.purple {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}

.modal-form-section-motion-icon.orange {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}

.modal-form-section-motion-icon.red {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.modal-form-section-motion-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Modal form fields */
.modal-form-field {
  /* Container for form field */
  display: flex;
  flex-direction: column;
}

.modal-form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.modal-form-grid.no-bottom-space {
  margin-bottom: 0;
}

.modal-form-grid.no-bottom-space > div:last-child {
  margin-bottom: 0;
}

.modal-form-grid.compact {
  gap: 0.75rem;
}

.modal-form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
}

.modal-required {
  color: #dc2626;
  font-weight: 600;
}

.modal-form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.modal-form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-form-input-error {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.modal-form-input-error:focus {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.modal-form-input.with-icon {
  padding-left: 2.5rem;
}

.modal-form-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.modal-form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
  resize: vertical;
}

.modal-form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-form-textarea.modal-form-input-error {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.modal-form-select.modal-form-input-error {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.modal-form-checkbox.modal-form-input-error {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.modal-form-checkbox {
  margin-top: 0.25rem;
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.modal-form-checkbox:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.modal-form-error {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.modal-form-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  height: 1.25rem;
  width: 1.25rem;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

/* Modal buttons */
.modal-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-weight: 500;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.modal-button:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 2px;
}
.modal-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.modal-button.primary {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.modal-button.primary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.modal-button.primary:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.modal-button.secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.modal-button.secondary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.modal-button.secondary:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(107 114 128 / var(--tw-ring-opacity, 1));
}

.modal-button.outline {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.modal-button.outline:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.modal-button.outline:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.modal-button.danger {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.modal-button.danger:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.modal-button.danger:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

.modal-button.success {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.modal-button.success:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}

.modal-button.success:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.modal-button.warning {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.modal-button.warning:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(161 98 7 / var(--tw-bg-opacity, 1));
}

.modal-button.warning:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(234 179 8 / var(--tw-ring-opacity, 1));
}

.modal-button.sm {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.modal-button.md {
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.modal-button.lg {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
}

.modal-button-loading-spinner {
  margin-right: 0.5rem;
  height: 1rem;
  width: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.modal-button-loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 9999px;
  border-bottom-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

/* Sample modal button styles - using buttons.css classes instead */

/* Modal cards & sections */
.modal-card {
  border-radius: 0.5rem;
  border-width: 1px;
  padding: 1rem;
}

.modal-card.blue-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);
}

.modal-card.gray {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.modal-card.white {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  background-color: transparent;
}

.modal-card-header {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-card-title {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-card-amount {
  text-align: right;
}

.modal-card-amount-value {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.modal-card-amount-label {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.modal-card-content > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.modal-card-content {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.modal-card-row {
  display: flex;
  justify-content: space-between;
}

.modal-card-row-label {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.modal-card-row-value {
  font-weight: 500;
}

.modal-card-row.discount {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.modal-card-row.fee {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

/* Modal dropdown styles */
.modal-dropdown {
  position: relative;
}

.modal-dropdown-button {
  width: 100%;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: left;
}

.modal-dropdown-button:focus {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.modal-dropdown-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-dropdown-selection {
  display: flex;
  align-items: center;
}

.modal-dropdown-icon {
  margin-right: 0.75rem;
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.modal-dropdown-text {
  /* Container for dropdown text */
  display: flex;
  flex-direction: column;
}

.modal-dropdown-title {
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-dropdown-description {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.modal-dropdown-arrow {
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.modal-dropdown-arrow.open {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.modal-dropdown-menu {
  position: absolute;
  margin-top: 0.25rem;
  max-height: 15rem;
  width: 100%;
  overflow: auto;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  background-color: transparent;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.modal-dropdown-item {
  width: 100%;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: left;
}

.modal-dropdown-item:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.modal-dropdown-item:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.modal-dropdown-item.selected {
  border-left-width: 4px;
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.modal-dropdown-item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-dropdown-item-icon {
  margin-right: 0.75rem;
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.modal-dropdown-item-text {
  /* Container for item text */
  display: flex;
  flex-direction: column;
}

.modal-dropdown-item-title {
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-dropdown-item-description {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.modal-dropdown-item-fee {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

/* Modal status messages */
.modal-message {
  border-radius: 0.5rem;
  border-width: 1px;
  padding: 0.75rem;
}

.modal-message.success {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.modal-message.error {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.modal-message.warning {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}

.modal-message.info {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.modal-message-content {
  display: flex;
  align-items: center;
}

.modal-message-icon {
  margin-right: 0.5rem;
  height: 1.25rem;
  width: 1.25rem;
}

.modal-message-icon.success {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.modal-message-icon.error {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.modal-message-icon.warning {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}

.modal-message-icon.info {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.modal-message-text {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
}

.modal-message-text.success {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.modal-message-text.error {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.modal-message-text.warning {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}

.modal-message-text.info {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

/* Modal checkbox sections */
.modal-checkbox-section > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.modal-checkbox-item {
  display: flex;
  align-items: flex-start;
}

.modal-checkbox-label {
  margin-left: 0.75rem;
}

.modal-checkbox-title {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-checkbox-description {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.modal-checkbox-input {
  margin-top: 0.5rem;
}

/* Modal promo code section */
.modal-promo-input {
  width: 100%;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-transform: uppercase;
}
.modal-promo-input:focus {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.modal-promo-success {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.modal-promo-success-icon {
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.modal-promo-message {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

/* Modal security & payment sections */
.modal-security-section {
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
  padding: 1rem;
}

.modal-security-header {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
}

.modal-security-icon {
  margin-right: 0.5rem;
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.modal-security-title {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-security-status {
  margin-bottom: 0.75rem;
  border-radius: 0.5rem;
  border-width: 1px;
  padding: 0.75rem;
}

.modal-security-status.creating {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.modal-security-status.ready {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.modal-security-status-content {
  display: flex;
  align-items: center;
}

.modal-security-status-spinner {
  margin-right: 0.5rem;
  height: 1rem;
  width: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.modal-security-status-spinner {
  animation: spin 1s linear infinite;
  border-radius: 9999px;
  border-bottom-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}

.modal-security-status-icon {
  margin-right: 0.5rem;
  height: 1rem;
  width: 1rem;
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.modal-security-status-text {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.modal-security-status-text.creating {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.modal-security-status-text.ready {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.modal-security-card {
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding: 0.75rem;
}

.modal-security-footer {
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.modal-security-footer-icon {
  margin-right: 0.25rem;
  height: 0.75rem;
  width: 0.75rem;
}

/* Modal resize handle - invisible border handles like sample modal */
.modal-resize-handle {
  position: absolute;
  background: transparent;
}

/* Border resize handles - 4px borders like sample modal */
.modal-resize-handle-top {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  cursor: n-resize;
}

.modal-resize-handle-right {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 4px;
  cursor: e-resize;
}

.modal-resize-handle-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  cursor: s-resize;
}

.modal-resize-handle-left {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 4px;
  cursor: w-resize;
}

/* Note: All resize handle variants removed - using border handles only like sample modal */

/* Ensure no corner resize handles appear - only border handles */
.modal-container {
  resize: none;
  overflow: hidden;
}

.modal-container * {
  resize: none;
}

/* Prevent any browser default resize handles */
.modal-container,
.modal-container *,
.modal-container::before,
.modal-container::after {
  resize: none;
  overflow: hidden;
}


/* Modal content specific styles */
.modal-project-content {
  height: calc(100% - 80px);
}

.modal-blog-content {
  cursor: default;
}

.modal-blog-form-content {
  /* Dynamic max-height and min-height will be set via inline styles */
  position: relative;
}

.modal-blog-view-mode-text {
  background-color: transparent;
}

/* ========================================
   MODAL FORM SECTIONS
   ======================================== */

.modal-form-section {
  margin-bottom: 1rem;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 0.75rem;
}

.modal-form-section.sample-style {
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  margin-bottom: 0;
}

.modal-form-section-header.sample-style {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.modal-form-section-title.sample-style {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.modal-form-section-icon.sample-style {
  width: 16px;
  height: 16px;
  color: #3b82f6;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.modal-form-section-icon.sample-style.green {
  color: #10b981;
}

.modal-form-section-icon.sample-style.orange {
  color: #f59e0b;
}

.modal-form-section-header {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
}

.modal-form-section-header > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.modal-form-section-icon {
  height: 1rem;
  width: 1rem;
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.modal-form-section-title {
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-form-section-content > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.modal-form-section-content.sample-style > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.modal-form-grid.sample-style {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0px;
}

.modal-form-input.sample-style {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.modal-form-input.sample-style:focus {
  border-color: #3b82f6;
  outline: none;
}

.modal-form-select.sample-style {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.modal-form-select.sample-style:focus {
  border-color: #3b82f6;
  outline: none;
}

.modal-form-textarea.sample-style {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  resize: vertical;
  font-family: inherit;
  transition: border-color 0.2s ease;
}

.modal-form-textarea.sample-style:focus {
  border-color: #3b82f6;
  outline: none;
}

/* ========================================
   MODAL CONTENT STRUCTURE
   ======================================== */

.modal-content {
  display: flex;
  flex-direction: column;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  padding: 0px;
}

.modal-content-body {
  flex: 1 1 0%;
  cursor: default;
}

.modal-content.sample-style {
  display: flex;
  flex-direction: column;
  opacity: 1;
  background-color: transparent;
  flex: 1;
}

.modal-content-body.sample-style {
  flex: 1;
  padding: 20px;
  cursor: default;
  overflow-y: visible;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.modal-content-wrapper.sample-style {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.modal-form > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

/* ========================================
   MODAL FOOTER
   ======================================== */

.modal-footer {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
  padding: 0.75rem;
}

.modal-footer-buttons {
  display: flex;
  justify-content: center;
}

.modal-footer-buttons > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.modal-footer.sample-style {
  background-color: white;
  padding: 24px 24px 0 24px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  min-height: 60px;
  opacity: 1;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

.modal-footer.no-padding {
  padding: 6px 8px 0 8px;
}

.modal-footer-buttons.sample-style {
  display: flex;
  gap: 12px;
  justify-content: center;
  align-items: center;
  padding: 8px 16px;
  min-height: 48px;
}

/* Modal status message styles (matching sample modal) */
.modal-status-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64748b;
  font-size: 14px;
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
}

.modal-status-message svg {
  width: 16px;
  height: 16px;
  color: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

/* ========================================
   MODAL RESIZE HANDLE
   ======================================== */

/* Note: Modal resize handles are defined above as invisible border handles */

/* ========================================
   MODAL HEADER COMPONENTS
   ======================================== */

.modal-header-icon {
  border-radius: 0.375rem;
  background-color: rgb(0 0 0 / 0.1);
  padding: 0.25rem;
}

.modal-header-icon-svg {
  height: 2rem;
  width: 2rem;
  color: transparent;
}

.modal-header-content {
  flex: 1 1 0%;
}

.modal-header-title {
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: unset;
}

.modal-header-subtitle {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  color: rgb(255 255 255 / 0.8);
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: unset;
}

.modal-close-button {
  border-radius: 0.375rem;
  padding: 0.25rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.modal-close-button:hover {
  background-color: rgb(255 255 255 / 0.1);
}

.modal-close-icon {
  height: 1.5rem;
  width: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

/* ========================================
   ADMIN/CLIENT DASHBOARD MODAL OVERRIDES
   ======================================== */

.admin-page .modal-container,
.admin-page .modal-container-motion,
.client-page .modal-container,
.client-page .modal-container-motion {
  background-color: #ffffff;
  color: #111827;
  border-color: #e5e7eb;
}

/* Modal form inputs override for admin/client dashboards */
.admin-page .modal-form-input,
.admin-page .modal-form-select,
.admin-page .modal-form-textarea,
.client-page .modal-form-input,
.client-page .modal-form-select,
.client-page .modal-form-textarea {
  background-color: #ffffff;
  color: #111827;
  border-color: #d1d5db;
}

.admin-page .modal-form-input::-moz-placeholder, .admin-page .modal-form-textarea::-moz-placeholder, .client-page .modal-form-input::-moz-placeholder, .client-page .modal-form-textarea::-moz-placeholder {
  color: #9ca3af;
}

.admin-page .modal-form-input::placeholder,
.admin-page .modal-form-textarea::placeholder,
.client-page .modal-form-input::placeholder,
.client-page .modal-form-textarea::placeholder {
  color: #9ca3af;
}

.modal-theme-blue {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}

.modal-theme-green {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}

/* Color utility classes for dynamic styling */
.modal-color-blue {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.modal-color-green {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.modal-color-purple {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}

.modal-color-orange {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}

.modal-color-red {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

/* Required field indicator */
.modal-required {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

/* Resize handle SVG */
.modal-resize-svg {
  height: 1rem;
  width: 1rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

/* Modal accessibility */
.modal-focus-trap {
  /* Focus trap styles if needed */
  position: relative;
  outline: none;
}

.modal-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.modal-aria-hidden {
  aria-hidden: true;
}

/* ========================================
   FOOTER STYLES
   ======================================== */

/* Modal footer base */
.modal-footer {
  margin-top: auto;
  border-bottom-right-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding: 1rem;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.modal-footer.centered {
  display: flex;
  justify-content: center;
}

.modal-footer.centered > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.modal-footer.left-aligned {
  display: flex;
  justify-content: flex-end;
}

.modal-footer.left-aligned > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.modal-footer.compact {
  padding: 0.75rem;
}

.modal-footer-basic {
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
  padding: 1rem;
}

/* ========================================
   TRANSITIONS & ANIMATIONS
   ======================================== */

/* Modal animation utilities */
.modal-fade-in {
  transition-property: opacity;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-slide-in {
  transition-property: transform;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-scale-in {
  transition-property: all;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* ========================================
   INVOICE MODAL STYLES
   ======================================== */

.invoice-details-container {
  background-color: #e9ecef;
  border: 1px solid #e9ecef;
}

.invoice-details-title {
  color: #495057;
}

.invoice-details-icon {
  color: #6c757d;
}

.invoice-header-desc {
  background-color: #d1d5db;
  color: #374151;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.invoice-header-qty,
.invoice-header-price,
.invoice-header-total {
  background-color: #d1d5db;
  color: #374151;
}

.invoice-header-del {
  background-color: #d1d5db;
  color: #374151;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.invoice-item-row {
  margin: 0;
  padding: 2px 0;
}

.invoice-summary-container {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.invoice-summary-title {
  color: #495057;
}

.invoice-summary-icon {
  color: #6c757d;
}

.invoice-summary-subtotal {
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
}

.invoice-summary-subtotal-label {
  color: #1565c0;
}

.invoice-summary-subtotal-value {
  color: #0d47a1;
}

.invoice-summary-tax {
  background-color: #fff3e0;
  border: 1px solid #ffcc02;
}

.invoice-summary-tax-label {
  color: #e65100;
}

.invoice-summary-tax-value {
  color: #bf360c;
}

.invoice-summary-total {
  background-color: #e8f5e8;
  border: 1px solid #c8e6c9;
}

.invoice-summary-total-label {
  color: #2e7d32;
}

.invoice-summary-total-value {
  color: #1b5e20;
}

/* ========================================
   RESPONSIVE UTILITIES
   ======================================== */

@media (max-width: 768px) {
  .modal-form-grid {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .modal-header-content > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }
  
  .modal-header-icon svg {
    height: 1.5rem;
    width: 1.5rem;
  }
  
  .modal-header-title {
    font-size: 1rem;
    line-height: 1.5rem;
  }
  
  .modal-header-subtitle {
    font-size: 0.75rem;
    line-height: 1rem;
  }
}

