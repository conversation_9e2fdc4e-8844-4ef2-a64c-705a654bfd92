/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/service-options/route";
exports.ids = ["app/api/admin/service-options/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fservice-options%2Froute&page=%2Fapi%2Fadmin%2Fservice-options%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fservice-options%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fservice-options%2Froute&page=%2Fapi%2Fadmin%2Fservice-options%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fservice-options%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _Volumes_Files_Technoloway_New_Website_Technoloway_src_app_api_admin_service_options_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/admin/service-options/route.ts */ \"(rsc)/./src/app/api/admin/service-options/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/service-options/route\",\n        pathname: \"/api/admin/service-options\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/service-options/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/app/api/admin/service-options/route.ts\",\n    nextConfigOutput,\n    userland: _Volumes_Files_Technoloway_New_Website_Technoloway_src_app_api_admin_service_options_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/admin/service-options/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fservice-options%2Froute&page=%2Fapi%2Fadmin%2Fservice-options%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fservice-options%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/service-options/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/admin/service-options/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n\n\nasync function GET(request) {\n    const prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\n    try {\n        // Check database connection\n        await prisma.$connect();\n        console.log('Database connected successfully for GET service options');\n        const { searchParams } = new URL(request.url);\n        const serviceId = searchParams.get('serviceId');\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '50');\n        const search = searchParams.get('search') || '';\n        const status = searchParams.get('status') || '';\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {};\n        if (serviceId) {\n            where.servid = BigInt(serviceId);\n        }\n        if (search) {\n            where.OR = [\n                {\n                    optname: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    optdesc: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        if (status === 'active') {\n            where.isactive = true;\n        } else if (status === 'inactive') {\n            where.isactive = false;\n        }\n        // Get service options with pagination\n        const [options, total] = await Promise.all([\n            prisma.serviceoptions.findMany({\n                where,\n                skip,\n                take: limit,\n                orderBy: [\n                    {\n                        createdat: 'asc'\n                    }\n                ],\n                include: {\n                    services: {\n                        select: {\n                            id: true,\n                            name: true,\n                            categories: {\n                                select: {\n                                    id: true,\n                                    categname: true\n                                }\n                            }\n                        }\n                    },\n                    _count: {\n                        select: {\n                            serviceoptionfeatures: true,\n                            orderdetails: true\n                        }\n                    }\n                }\n            }),\n            prisma.serviceoptions.count({\n                where\n            })\n        ]);\n        // Transform the data to match the frontend interface\n        const transformedOptions = options.map((option)=>({\n                id: option.id.toString(),\n                serviceId: option.servid.toString(),\n                name: option.optname,\n                description: option.optdesc || '',\n                price: option.optprice ? parseFloat(option.optprice.toString()) : 0,\n                discountRate: option.optdiscountrate || 0,\n                totalDiscount: option.opttotaldiscount || 0,\n                displayOrder: 0,\n                isActive: option.isactive,\n                createdAt: option.createdat?.toISOString() || new Date().toISOString(),\n                updatedAt: option.updatedat?.toISOString() || new Date().toISOString(),\n                service: option.services ? {\n                    id: option.services.id.toString(),\n                    name: option.services.name,\n                    category: option.services.categories ? {\n                        id: option.services.categories.id.toString(),\n                        name: option.services.categories.categname\n                    } : undefined\n                } : undefined,\n                _count: {\n                    features: option._count.serviceoptionfeatures,\n                    orderDetails: option._count.orderdetails\n                }\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            data: transformedOptions,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error('Error fetching service options:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch service options'\n        }, {\n            status: 500\n        });\n    } finally{\n        await prisma.$disconnect();\n        console.log('Database disconnected for GET service options');\n    }\n}\nasync function POST(request) {\n    const prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\n    try {\n        // Check database connection\n        await prisma.$connect();\n        console.log('Database connected successfully for POST service option');\n        const body = await request.json();\n        console.log('POST service option request body:', body);\n        const { name, description, serviceid, price, discountrate, totaldiscount, isactive } = body;\n        // Validate required fields\n        if (!name || !serviceid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields: name, serviceid'\n            }, {\n                status: 400\n            });\n        }\n        // Check if service exists\n        const serviceExists = await prisma.services.findUnique({\n            where: {\n                id: BigInt(serviceid)\n            }\n        });\n        if (!serviceExists) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Service not found'\n            }, {\n                status: 400\n            });\n        }\n        const option = await prisma.serviceoptions.create({\n            data: {\n                optname: name,\n                optdesc: description || '',\n                servid: BigInt(serviceid),\n                optprice: price ? parseFloat(price) : 0,\n                optdiscountrate: discountrate ? parseInt(discountrate) : 0,\n                opttotaldiscount: totaldiscount ? parseFloat(totaldiscount) : 0,\n                isactive: isactive ?? true\n            }\n        });\n        console.log('Service option created successfully:', option);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Service option created successfully',\n            data: {\n                id: option.id.toString(),\n                name: option.optname,\n                description: option.optdesc || '',\n                serviceId: option.servid.toString(),\n                price: option.optprice ? parseFloat(option.optprice.toString()) : 0,\n                discountRate: option.optdiscountrate || 0,\n                totalDiscount: option.opttotaldiscount || 0,\n                displayOrder: 0,\n                isActive: option.isactive,\n                createdAt: option.createdat?.toISOString() || new Date().toISOString(),\n                updatedAt: option.updatedat?.toISOString() || new Date().toISOString()\n            }\n        });\n    } catch (error) {\n        console.error('Error creating service option:', error);\n        // Handle specific Prisma errors\n        if (error instanceof Error && 'code' in error && error.code === 'P2002') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Service option with this name already exists in this service'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to create service option',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    } finally{\n        await prisma.$disconnect();\n        console.log('Database disconnected for POST service option');\n    }\n}\nasync function PUT(request) {\n    const prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\n    try {\n        await prisma.$connect();\n        console.log('Database connected successfully for PUT service options');\n        const body = await request.json();\n        const { ids, data } = body;\n        if (!ids || !Array.isArray(ids)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'IDs array is required'\n            }, {\n                status: 400\n            });\n        }\n        // Update multiple options (for bulk actions)\n        const updatePromises = ids.map(async (id)=>{\n            return prisma.serviceoptions.update({\n                where: {\n                    id: parseInt(id)\n                },\n                data: {\n                    ...data.name && {\n                        optname: data.name\n                    },\n                    ...data.description !== undefined && {\n                        optdesc: data.description\n                    },\n                    ...data.price !== undefined && {\n                        optprice: parseFloat(data.price)\n                    },\n                    ...data.discountRate !== undefined && {\n                        optdiscountrate: data.discountRate\n                    },\n                    ...data.totalDiscount !== undefined && {\n                        opttotaldiscount: data.totalDiscount\n                    },\n                    ...data.isActive !== undefined && {\n                        isactive: data.isActive\n                    },\n                    updatedat: new Date()\n                }\n            });\n        });\n        await Promise.all(updatePromises);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Service options updated successfully'\n        });\n    } catch (error) {\n        console.error('Error updating service options:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to update service options'\n        }, {\n            status: 500\n        });\n    } finally{\n        await prisma.$disconnect();\n        console.log('Database disconnected for PUT service options');\n    }\n}\nasync function DELETE(request) {\n    const prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\n    try {\n        await prisma.$connect();\n        console.log('Database connected successfully for DELETE service options');\n        const body = await request.json();\n        const { ids } = body;\n        if (!ids || !Array.isArray(ids)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'IDs array is required'\n            }, {\n                status: 400\n            });\n        }\n        // Delete multiple options (for bulk actions)\n        const deletePromises = ids.map(async (id)=>{\n            return prisma.serviceoptions.delete({\n                where: {\n                    id: parseInt(id)\n                }\n            });\n        });\n        await Promise.all(deletePromises);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Service options deleted successfully'\n        });\n    } catch (error) {\n        console.error('Error deleting service options:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to delete service options'\n        }, {\n            status: 500\n        });\n    } finally{\n        await prisma.$disconnect();\n        console.log('Database disconnected for DELETE service options');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/service-options/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fservice-options%2Froute&page=%2Fapi%2Fadmin%2Fservice-options%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fservice-options%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();